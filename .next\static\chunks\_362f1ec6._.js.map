{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/app/onboarding/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuthStore } from '@/store/useAuthStore';\r\nimport { supabase } from '@/lib/supabase';\r\nimport { Clock, Calendar, Coffee, Moon, Sun, ArrowRight } from 'lucide-react';\r\n\r\ninterface TimeSlot {\r\n  start: string;\r\n  end: string;\r\n  type: 'work' | 'sleep' | 'meal' | 'commute' | 'personal';\r\n  label: string;\r\n}\r\n\r\nexport default function Onboarding() {\r\n  const router = useRouter();\r\n  const { user } = useAuthStore();\r\n  \r\n  const [currentStep, setCurrentStep] = useState(1);\r\n  const [loading, setLoading] = useState(false);\r\n  \r\n  // 用户时间配置\r\n  const [timeConfig, setTimeConfig] = useState({\r\n    // 工作时间\r\n    workStart: '09:00',\r\n    workEnd: '18:00',\r\n    workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],\r\n    \r\n    // 睡眠时间\r\n    sleepStart: '23:00',\r\n    sleepEnd: '07:00',\r\n    \r\n    // 固定时间段\r\n    fixedSlots: [\r\n      { start: '07:00', end: '08:00', type: 'personal', label: '晨间例行' },\r\n      { start: '12:00', end: '13:00', type: 'meal', label: '午餐时间' },\r\n      { start: '18:30', end: '19:30', type: 'meal', label: '晚餐时间' },\r\n    ] as TimeSlot[],\r\n    \r\n    // 通勤时间\r\n    commuteToWork: 30, // 分钟\r\n    commuteFromWork: 30,\r\n    \r\n    // 个人偏好\r\n    preferredWorkHours: 'morning', // morning, afternoon, evening\r\n    maxContinuousWork: 120, // 最大连续工作时间（分钟）\r\n    breakInterval: 25, // 休息间隔（分钟）\r\n    \r\n    // 分类时间偏好\r\n    categoryPreferences: {\r\n      work: { preferredTimes: ['09:00-12:00', '14:00-17:00'], maxDaily: 480 },\r\n      improvement: { preferredTimes: ['19:00-21:00', '07:00-08:00'], maxDaily: 120 },\r\n      entertainment: { preferredTimes: ['20:00-22:00'], maxDaily: 180 }\r\n    }\r\n  });\r\n\r\n  const handleTimeChange = (field: string, value: any) => {\r\n    setTimeConfig(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n  };\r\n\r\n  const handleFixedSlotChange = (index: number, field: string, value: string) => {\r\n    setTimeConfig(prev => ({\r\n      ...prev,\r\n      fixedSlots: prev.fixedSlots.map((slot, i) => \r\n        i === index ? { ...slot, [field]: value } : slot\r\n      )\r\n    }));\r\n  };\r\n\r\n  const addFixedSlot = () => {\r\n    setTimeConfig(prev => ({\r\n      ...prev,\r\n      fixedSlots: [...prev.fixedSlots, {\r\n        start: '20:00',\r\n        end: '21:00',\r\n        type: 'personal',\r\n        label: '自定义时间'\r\n      }]\r\n    }));\r\n  };\r\n\r\n  const removeFixedSlot = (index: number) => {\r\n    setTimeConfig(prev => ({\r\n      ...prev,\r\n      fixedSlots: prev.fixedSlots.filter((_, i) => i !== index)\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!user) return;\r\n\r\n    setLoading(true);\r\n    try {\r\n      console.log('Attempting to save user configuration...'); // 尝试保存用户配置\r\n      console.log('User ID:', user.id);\r\n      console.log('User Email:', user.email);\r\n      console.log('Time Config:', timeConfig);\r\n\r\n      // 首先检查数据库连接和表结构\r\n      console.log('Testing database connection...');\r\n      const { data: testConnection, error: connectionError } = await supabase\r\n        .from('user_profiles')\r\n        .select('count', { count: 'exact', head: true });\r\n\r\n      if (connectionError) {\r\n        console.error('Database connection failed:', connectionError);\r\n        throw new Error(`数据库连接失败: ${connectionError.message}`);\r\n      }\r\n\r\n      console.log('Database connection successful');\r\n\r\n      // 检查用户是否已存在\r\n      const { data: existingProfile, error: fetchError } = await supabase\r\n        .from('user_profiles')\r\n        .select('*')\r\n        .eq('id', user.id)\r\n        .single();\r\n\r\n      console.log('Existing profile:', existingProfile);\r\n      console.log('Fetch error:', fetchError);\r\n\r\n      // 准备要保存的数据\r\n      const profileData = {\r\n        id: user.id,\r\n        email: user.email!,\r\n        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\r\n        work_hours: {\r\n          start: timeConfig.workStart,\r\n          end: timeConfig.workEnd,\r\n          days: timeConfig.workDays\r\n        }\r\n      };\r\n\r\n      // 如果表中有 time_config 和 onboarding_completed 字段，则添加它们\r\n      try {\r\n        const extendedData = {\r\n          ...profileData,\r\n          time_config: timeConfig,\r\n          onboarding_completed: true\r\n        };\r\n\r\n        console.log('Attempting to save with extended data:', extendedData);\r\n\r\n        const { data, error } = await supabase\r\n          .from('user_profiles')\r\n          .upsert(extendedData, { onConflict: 'id' })\r\n          .select();\r\n\r\n        console.log('Save result:', { data, error });\r\n\r\n        if (error) {\r\n          console.error('Extended save failed, trying basic save:', error);\r\n\r\n          // 如果扩展保存失败，尝试基本保存\r\n          const { data: basicData, error: basicError } = await supabase\r\n            .from('user_profiles')\r\n            .upsert(profileData, { onConflict: 'id' })\r\n            .select();\r\n\r\n          console.log('Basic save result:', { data: basicData, error: basicError });\r\n\r\n          if (basicError) throw basicError;\r\n\r\n          alert('配置已保存，但部分高级功能可能不可用。请联系管理员更新数据库结构。');\r\n        }\r\n\r\n      } catch (extendedError) {\r\n        console.error('Extended save failed:', extendedError);\r\n        throw extendedError;\r\n      }\r\n\r\n      router.push('/dashboard');\r\n    } catch (error) {\r\n      console.error('Failed to save time configuration:', error);\r\n      console.error('Error details:', JSON.stringify(error, null, 2));\r\n\r\n      // 提供更详细的错误信息\r\n      let errorMessage = '保存配置失败，请重试';\r\n      if (error && typeof error === 'object') {\r\n        if ('message' in error) {\r\n          errorMessage = `保存失败: ${error.message}`;\r\n        } else if ('details' in error) {\r\n          errorMessage = `保存失败: ${error.details}`;\r\n        }\r\n      }\r\n\r\n      alert(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const calculateAvailableTime = () => {\r\n    // 计算每日可用时间\r\n    const workMinutes = calculateTimeDiff(timeConfig.workStart, timeConfig.workEnd);\r\n    const sleepMinutes = calculateTimeDiff(timeConfig.sleepStart, timeConfig.sleepEnd);\r\n    const fixedMinutes = timeConfig.fixedSlots.reduce((sum, slot) => \r\n      sum + calculateTimeDiff(slot.start, slot.end), 0\r\n    );\r\n    const commuteMinutes = timeConfig.commuteToWork + timeConfig.commuteFromWork;\r\n    \r\n    const totalDayMinutes = 24 * 60;\r\n    const availableMinutes = totalDayMinutes - sleepMinutes - fixedMinutes - commuteMinutes;\r\n    const workAvailableMinutes = workMinutes;\r\n    const personalAvailableMinutes = availableMinutes - workMinutes;\r\n    \r\n    return {\r\n      total: availableMinutes,\r\n      work: workAvailableMinutes,\r\n      personal: personalAvailableMinutes\r\n    };\r\n  };\r\n\r\n  const calculateTimeDiff = (start: string, end: string) => {\r\n    const [startHour, startMin] = start.split(':').map(Number);\r\n    const [endHour, endMin] = end.split(':').map(Number);\r\n    \r\n    let startMinutes = startHour * 60 + startMin;\r\n    let endMinutes = endHour * 60 + endMin;\r\n    \r\n    // 处理跨天情况\r\n    if (endMinutes < startMinutes) {\r\n      endMinutes += 24 * 60;\r\n    }\r\n    \r\n    return endMinutes - startMinutes;\r\n  };\r\n\r\n  const availableTime = calculateAvailableTime();\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-8\">\r\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">欢迎使用 TimeManager</h1>\r\n          <p className=\"text-gray-600\">让我们了解您的时间安排，为您提供个性化的时间规划</p>\r\n        </div>\r\n\r\n        {/* Progress */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center justify-center space-x-4\">\r\n            {[1, 2, 3].map((step) => (\r\n              <div key={step} className=\"flex items-center\">\r\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\r\n                  currentStep >= step \r\n                    ? 'bg-indigo-600 text-white' \r\n                    : 'bg-gray-200 text-gray-600'\r\n                }`}>\r\n                  {step}\r\n                </div>\r\n                {step < 3 && (\r\n                  <div className={`w-16 h-1 mx-2 ${\r\n                    currentStep > step ? 'bg-indigo-600' : 'bg-gray-200'\r\n                  }`} />\r\n                )}\r\n              </div>\r\n            ))}\r\n          </div>\r\n          <div className=\"flex justify-center mt-2\">\r\n            <span className=\"text-sm text-gray-500\">\r\n              {currentStep === 1 && '工作时间设置'}\r\n              {currentStep === 2 && '生活时间安排'}\r\n              {currentStep === 3 && '个人偏好配置'}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white rounded-lg shadow p-6\">\r\n          {/* Step 1: 工作时间 */}\r\n          {currentStep === 1 && (\r\n            <div className=\"space-y-6\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <Clock className=\"h-6 w-6 text-indigo-600 mr-2\" />\r\n                <h2 className=\"text-xl font-semibold\">工作时间设置</h2>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    工作开始时间\r\n                  </label>\r\n                  <input\r\n                    type=\"time\"\r\n                    value={timeConfig.workStart}\r\n                    onChange={(e) => handleTimeChange('workStart', e.target.value)}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    工作结束时间\r\n                  </label>\r\n                  <input\r\n                    type=\"time\"\r\n                    value={timeConfig.workEnd}\r\n                    onChange={(e) => handleTimeChange('workEnd', e.target.value)}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  工作日\r\n                </label>\r\n                <div className=\"grid grid-cols-7 gap-2\">\r\n                  {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day, index) => {\r\n                    const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];\r\n                    const isSelected = timeConfig.workDays.includes(day);\r\n                    \r\n                    return (\r\n                      <button\r\n                        key={day}\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          const newWorkDays = isSelected\r\n                            ? timeConfig.workDays.filter(d => d !== day)\r\n                            : [...timeConfig.workDays, day];\r\n                          handleTimeChange('workDays', newWorkDays);\r\n                        }}\r\n                        className={`p-2 text-sm rounded-md border ${\r\n                          isSelected\r\n                            ? 'bg-indigo-600 text-white border-indigo-600'\r\n                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'\r\n                        }`}\r\n                      >\r\n                        {dayNames[index]}\r\n                      </button>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    上班通勤时间（分钟）\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    max=\"180\"\r\n                    value={timeConfig.commuteToWork}\r\n                    onChange={(e) => handleTimeChange('commuteToWork', parseInt(e.target.value))}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    下班通勤时间（分钟）\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    min=\"0\"\r\n                    max=\"180\"\r\n                    value={timeConfig.commuteFromWork}\r\n                    onChange={(e) => handleTimeChange('commuteFromWork', parseInt(e.target.value))}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Step 2: 生活时间 */}\r\n          {currentStep === 2 && (\r\n            <div className=\"space-y-6\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <Moon className=\"h-6 w-6 text-indigo-600 mr-2\" />\r\n                <h2 className=\"text-xl font-semibold\">生活时间安排</h2>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    睡觉时间\r\n                  </label>\r\n                  <input\r\n                    type=\"time\"\r\n                    value={timeConfig.sleepStart}\r\n                    onChange={(e) => handleTimeChange('sleepStart', e.target.value)}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    起床时间\r\n                  </label>\r\n                  <input\r\n                    type=\"time\"\r\n                    value={timeConfig.sleepEnd}\r\n                    onChange={(e) => handleTimeChange('sleepEnd', e.target.value)}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <h3 className=\"text-lg font-medium text-gray-900\">固定时间段</h3>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={addFixedSlot}\r\n                    className=\"px-3 py-1 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700\"\r\n                  >\r\n                    添加时间段\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"space-y-3\">\r\n                  {timeConfig.fixedSlots.map((slot, index) => (\r\n                    <div key={index} className=\"grid grid-cols-12 gap-3 items-center\">\r\n                      <div className=\"col-span-3\">\r\n                        <input\r\n                          type=\"time\"\r\n                          value={slot.start}\r\n                          onChange={(e) => handleFixedSlotChange(index, 'start', e.target.value)}\r\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded-md\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"col-span-3\">\r\n                        <input\r\n                          type=\"time\"\r\n                          value={slot.end}\r\n                          onChange={(e) => handleFixedSlotChange(index, 'end', e.target.value)}\r\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded-md\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"col-span-4\">\r\n                        <input\r\n                          type=\"text\"\r\n                          value={slot.label}\r\n                          onChange={(e) => handleFixedSlotChange(index, 'label', e.target.value)}\r\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded-md\"\r\n                          placeholder=\"时间段名称\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"col-span-2\">\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => removeFixedSlot(index)}\r\n                          className=\"w-full px-2 py-1 text-sm text-red-600 hover:text-red-800\"\r\n                        >\r\n                          删除\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Step 3: 个人偏好 */}\r\n          {currentStep === 3 && (\r\n            <div className=\"space-y-6\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <Sun className=\"h-6 w-6 text-indigo-600 mr-2\" />\r\n                <h2 className=\"text-xl font-semibold\">个人偏好配置</h2>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  最佳工作时段\r\n                </label>\r\n                <select\r\n                  value={timeConfig.preferredWorkHours}\r\n                  onChange={(e) => handleTimeChange('preferredWorkHours', e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                >\r\n                  <option value=\"morning\">上午（精力充沛）</option>\r\n                  <option value=\"afternoon\">下午（稳定高效）</option>\r\n                  <option value=\"evening\">晚上（思维活跃）</option>\r\n                </select>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    最大连续工作时间（分钟）\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    min=\"30\"\r\n                    max=\"240\"\r\n                    step=\"15\"\r\n                    value={timeConfig.maxContinuousWork}\r\n                    onChange={(e) => handleTimeChange('maxContinuousWork', parseInt(e.target.value))}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    休息间隔（分钟）\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    min=\"5\"\r\n                    max=\"60\"\r\n                    step=\"5\"\r\n                    value={timeConfig.breakInterval}\r\n                    onChange={(e) => handleTimeChange('breakInterval', parseInt(e.target.value))}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {/* 时间分析预览 */}\r\n              <div className=\"bg-gray-50 rounded-lg p-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 mb-3\">每日时间分析</h3>\r\n                <div className=\"grid grid-cols-3 gap-4 text-center\">\r\n                  <div>\r\n                    <p className=\"text-2xl font-bold text-blue-600\">{Math.round(availableTime.work / 60 * 10) / 10}h</p>\r\n                    <p className=\"text-sm text-gray-600\">工作时间</p>\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"text-2xl font-bold text-green-600\">{Math.round(availableTime.personal / 60 * 10) / 10}h</p>\r\n                    <p className=\"text-sm text-gray-600\">个人时间</p>\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"text-2xl font-bold text-purple-600\">{Math.round(availableTime.total / 60 * 10) / 10}h</p>\r\n                    <p className=\"text-sm text-gray-600\">总可用时间</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Navigation */}\r\n          <div className=\"flex justify-between mt-8\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}\r\n              disabled={currentStep === 1}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              上一步\r\n            </button>\r\n\r\n            {currentStep < 3 ? (\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setCurrentStep(currentStep + 1)}\r\n                className=\"flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700\"\r\n              >\r\n                下一步\r\n                <ArrowRight className=\"ml-2 h-4 w-4\" />\r\n              </button>\r\n            ) : (\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleSubmit}\r\n                disabled={loading}\r\n                className=\"flex items-center px-6 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 disabled:opacity-50\"\r\n              >\r\n                {loading ? (\r\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                ) : (\r\n                  <Coffee className=\"mr-2 h-4 w-4\" />\r\n                )}\r\n                完成设置\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAee,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,SAAS;IACT,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,WAAW;QACX,SAAS;QACT,UAAU;YAAC;YAAU;YAAW;YAAa;YAAY;SAAS;QAElE,OAAO;QACP,YAAY;QACZ,UAAU;QAEV,QAAQ;QACR,YAAY;YACV;gBAAE,OAAO;gBAAS,KAAK;gBAAS,MAAM;gBAAY,OAAO;YAAO;YAChE;gBAAE,OAAO;gBAAS,KAAK;gBAAS,MAAM;gBAAQ,OAAO;YAAO;YAC5D;gBAAE,OAAO;gBAAS,KAAK;gBAAS,MAAM;gBAAQ,OAAO;YAAO;SAC7D;QAED,OAAO;QACP,eAAe;QACf,iBAAiB;QAEjB,OAAO;QACP,oBAAoB;QACpB,mBAAmB;QACnB,eAAe;QAEf,SAAS;QACT,qBAAqB;YACnB,MAAM;gBAAE,gBAAgB;oBAAC;oBAAe;iBAAc;gBAAE,UAAU;YAAI;YACtE,aAAa;gBAAE,gBAAgB;oBAAC;oBAAe;iBAAc;gBAAE,UAAU;YAAI;YAC7E,eAAe;gBAAE,gBAAgB;oBAAC;iBAAc;gBAAE,UAAU;YAAI;QAClE;IACF;IAEA,MAAM,mBAAmB,CAAC,OAAe;QACvC,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,wBAAwB,CAAC,OAAe,OAAe;QAC3D,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,IACrC,MAAM,QAAQ;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,IAAI;YAEhD,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,YAAY;uBAAI,KAAK,UAAU;oBAAE;wBAC/B,OAAO;wBACP,KAAK;wBACL,MAAM;wBACN,OAAO;oBACT;iBAAE;YACJ,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACrD,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,QAAQ,GAAG,CAAC,6CAA6C,WAAW;YACpE,QAAQ,GAAG,CAAC,YAAY,KAAK,EAAE;YAC/B,QAAQ,GAAG,CAAC,eAAe,KAAK,KAAK;YACrC,QAAQ,GAAG,CAAC,gBAAgB;YAE5B,gBAAgB;YAChB,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACpE,IAAI,CAAC,iBACL,MAAM,CAAC,SAAS;gBAAE,OAAO;gBAAS,MAAM;YAAK;YAEhD,IAAI,iBAAiB;gBACnB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,gBAAgB,OAAO,EAAE;YACvD;YAEA,QAAQ,GAAG,CAAC;YAEZ,YAAY;YACZ,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAChE,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;YAET,QAAQ,GAAG,CAAC,qBAAqB;YACjC,QAAQ,GAAG,CAAC,gBAAgB;YAE5B,WAAW;YACX,MAAM,cAAc;gBAClB,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;gBAC1D,YAAY;oBACV,OAAO,WAAW,SAAS;oBAC3B,KAAK,WAAW,OAAO;oBACvB,MAAM,WAAW,QAAQ;gBAC3B;YACF;YAEA,oDAAoD;YACpD,IAAI;gBACF,MAAM,eAAe;oBACnB,GAAG,WAAW;oBACd,aAAa;oBACb,sBAAsB;gBACxB;gBAEA,QAAQ,GAAG,CAAC,0CAA0C;gBAEtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,cAAc;oBAAE,YAAY;gBAAK,GACxC,MAAM;gBAET,QAAQ,GAAG,CAAC,gBAAgB;oBAAE;oBAAM;gBAAM;gBAE1C,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,4CAA4C;oBAE1D,kBAAkB;oBAClB,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,iBACL,MAAM,CAAC,aAAa;wBAAE,YAAY;oBAAK,GACvC,MAAM;oBAET,QAAQ,GAAG,CAAC,sBAAsB;wBAAE,MAAM;wBAAW,OAAO;oBAAW;oBAEvE,IAAI,YAAY,MAAM;oBAEtB,MAAM;gBACR;YAEF,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM;YACR;YAEA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,QAAQ,KAAK,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAE5D,aAAa;YACb,IAAI,eAAe;YACnB,IAAI,SAAS,OAAO,UAAU,UAAU;gBACtC,IAAI,aAAa,OAAO;oBACtB,eAAe,CAAC,MAAM,EAAE,MAAM,OAAO,EAAE;gBACzC,OAAO,IAAI,aAAa,OAAO;oBAC7B,eAAe,CAAC,MAAM,EAAE,MAAM,OAAO,EAAE;gBACzC;YACF;YAEA,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB;QAC7B,WAAW;QACX,MAAM,cAAc,kBAAkB,WAAW,SAAS,EAAE,WAAW,OAAO;QAC9E,MAAM,eAAe,kBAAkB,WAAW,UAAU,EAAE,WAAW,QAAQ;QACjF,MAAM,eAAe,WAAW,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,OACtD,MAAM,kBAAkB,KAAK,KAAK,EAAE,KAAK,GAAG,GAAG;QAEjD,MAAM,iBAAiB,WAAW,aAAa,GAAG,WAAW,eAAe;QAE5E,MAAM,kBAAkB,KAAK;QAC7B,MAAM,mBAAmB,kBAAkB,eAAe,eAAe;QACzE,MAAM,uBAAuB;QAC7B,MAAM,2BAA2B,mBAAmB;QAEpD,OAAO;YACL,OAAO;YACP,MAAM;YACN,UAAU;QACZ;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,MAAM,CAAC,WAAW,SAAS,GAAG,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC;QACnD,MAAM,CAAC,SAAS,OAAO,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;QAE7C,IAAI,eAAe,YAAY,KAAK;QACpC,IAAI,aAAa,UAAU,KAAK;QAEhC,SAAS;QACT,IAAI,aAAa,cAAc;YAC7B,cAAc,KAAK;QACrB;QAEA,OAAO,aAAa;IACtB;IAEA,MAAM,gBAAgB;IAEtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,qBACd,6LAAC;oCAAe,WAAU;;sDACxB,6LAAC;4CAAI,WAAW,CAAC,0EAA0E,EACzF,eAAe,OACX,6BACA,6BACJ;sDACC;;;;;;wCAEF,OAAO,mBACN,6LAAC;4CAAI,WAAW,CAAC,cAAc,EAC7B,cAAc,OAAO,kBAAkB,eACvC;;;;;;;mCAXI;;;;;;;;;;sCAgBd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;;oCACb,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;8BAK5B,6LAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,mBACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;;8CAGxC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,WAAW,SAAS;oDAC3B,UAAU,CAAC,IAAM,iBAAiB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC7D,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,WAAW,OAAO;oDACzB,UAAU,CAAC,IAAM,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC3D,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAU;gDAAW;gDAAa;gDAAY;gDAAU;gDAAY;6CAAS,CAAC,GAAG,CAAC,CAAC,KAAK;gDACxF,MAAM,WAAW;oDAAC;oDAAM;oDAAM;oDAAM;oDAAM;oDAAM;oDAAM;iDAAK;gDAC3D,MAAM,aAAa,WAAW,QAAQ,CAAC,QAAQ,CAAC;gDAEhD,qBACE,6LAAC;oDAEC,MAAK;oDACL,SAAS;wDACP,MAAM,cAAc,aAChB,WAAW,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,OACtC;+DAAI,WAAW,QAAQ;4DAAE;yDAAI;wDACjC,iBAAiB,YAAY;oDAC/B;oDACA,WAAW,CAAC,8BAA8B,EACxC,aACI,+CACA,2DACJ;8DAED,QAAQ,CAAC,MAAM;mDAdX;;;;;4CAiBX;;;;;;;;;;;;8CAIJ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,WAAW,aAAa;oDAC/B,UAAU,CAAC,IAAM,iBAAiB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1E,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,WAAW,eAAe;oDACjC,UAAU,CAAC,IAAM,iBAAiB,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC5E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;wBAQnB,gBAAgB,mBACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;;8CAGxC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,WAAW,UAAU;oDAC5B,UAAU,CAAC,IAAM,iBAAiB,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC9D,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,WAAW,QAAQ;oDAC1B,UAAU,CAAC,IAAM,iBAAiB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;sDAKH,6LAAC;4CAAI,WAAU;sDACZ,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,MAAK;gEACL,OAAO,KAAK,KAAK;gEACjB,UAAU,CAAC,IAAM,sBAAsB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;gEACrE,WAAU;;;;;;;;;;;sEAGd,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,MAAK;gEACL,OAAO,KAAK,GAAG;gEACf,UAAU,CAAC,IAAM,sBAAsB,OAAO,OAAO,EAAE,MAAM,CAAC,KAAK;gEACnE,WAAU;;;;;;;;;;;sEAGd,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,MAAK;gEACL,OAAO,KAAK,KAAK;gEACjB,UAAU,CAAC,IAAM,sBAAsB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;gEACrE,WAAU;gEACV,aAAY;;;;;;;;;;;sEAGhB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,gBAAgB;gEAC/B,WAAU;0EACX;;;;;;;;;;;;mDA/BK;;;;;;;;;;;;;;;;;;;;;;wBA2CnB,gBAAgB,mBACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;;8CAGxC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,WAAW,kBAAkB;4CACpC,UAAU,CAAC,IAAM,iBAAiB,sBAAsB,EAAE,MAAM,CAAC,KAAK;4CACtE,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;8CAI5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,MAAK;oDACL,OAAO,WAAW,iBAAiB;oDACnC,UAAU,CAAC,IAAM,iBAAiB,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC9E,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,MAAK;oDACL,OAAO,WAAW,aAAa;oDAC/B,UAAU,CAAC,IAAM,iBAAiB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1E,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;;gEAAoC,KAAK,KAAK,CAAC,cAAc,IAAI,GAAG,KAAK,MAAM;gEAAG;;;;;;;sEAC/F,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;;gEAAqC,KAAK,KAAK,CAAC,cAAc,QAAQ,GAAG,KAAK,MAAM;gEAAG;;;;;;;sEACpG,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;;gEAAsC,KAAK,KAAK,CAAC,cAAc,KAAK,GAAG,KAAK,MAAM;gEAAG;;;;;;;sEAClG,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;oCACxD,UAAU,gBAAgB;oCAC1B,WAAU;8CACX;;;;;;gCAIA,cAAc,kBACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,eAAe,cAAc;oCAC5C,WAAU;;wCACX;sDAEC,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;yDAGxB,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;wCAET,wBACC,6LAAC;4CAAI,WAAU;;;;;iEAEf,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAljBwB;;QACP,qIAAA,CAAA,YAAS;QACP,+HAAA,CAAA,eAAY;;;KAFP", "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1140, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uKAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,0KAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKACjF,gBAAA,4JAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,kLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,kLAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "file": "coffee.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/icons/coffee.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 2v2', key: '7u0qdc' }],\n  ['path', { d: 'M14 2v2', key: '6buw04' }],\n  [\n    'path',\n    {\n      d: 'M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1',\n      key: 'pwadti',\n    },\n  ],\n  ['path', { d: 'M6 2v2', key: 'colzsn' }],\n];\n\n/**\n * @component @name Coffee\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnYyIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjIiIC8+CiAgPHBhdGggZD0iTTE2IDhhMSAxIDAgMCAxIDEgMXY4YTQgNCAwIDAgMS00IDRIN2E0IDQgMCAwIDEtNC00VjlhMSAxIDAgMCAxIDEtMWgxNGE0IDQgMCAxIDEgMCA4aC0xIiAvPgogIDxwYXRoIGQ9Ik02IDJ2MiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/coffee\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Coffee = createLucideIcon('coffee', __iconNode);\n\nexport default Coffee;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzC;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1383, "column": 0}, "map": {"version": 3, "file": "moon.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/icons/moon.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z', key: 'a7tn18' }],\n];\n\n/**\n * @component @name Moon\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM2E2IDYgMCAwIDAgOSA5IDkgOSAwIDEgMS05LTlaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/moon\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Moon = createLucideIcon('moon', __iconNode);\n\nexport default Moon;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACrE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1422, "column": 0}, "map": {"version": 3, "file": "sun.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/icons/sun.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '4', key: '4exip2' }],\n  ['path', { d: 'M12 2v2', key: 'tus03m' }],\n  ['path', { d: 'M12 20v2', key: '1lh1kg' }],\n  ['path', { d: 'm4.93 4.93 1.41 1.41', key: '149t6j' }],\n  ['path', { d: 'm17.66 17.66 1.41 1.41', key: 'ptbguv' }],\n  ['path', { d: 'M2 12h2', key: '1t8f8n' }],\n  ['path', { d: 'M20 12h2', key: '1q8mjw' }],\n  ['path', { d: 'm6.34 17.66-1.41 1.41', key: '1m8zz5' }],\n  ['path', { d: 'm19.07 4.93-1.41 1.41', key: '1shlcs' }],\n];\n\n/**\n * @component @name Sun\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI0IiAvPgogIDxwYXRoIGQ9Ik0xMiAydjIiIC8+CiAgPHBhdGggZD0iTTEyIDIwdjIiIC8+CiAgPHBhdGggZD0ibTQuOTMgNC45MyAxLjQxIDEuNDEiIC8+CiAgPHBhdGggZD0ibTE3LjY2IDE3LjY2IDEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJNMiAxMmgyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxMmgyIiAvPgogIDxwYXRoIGQ9Im02LjM0IDE3LjY2LTEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJtMTkuMDcgNC45My0xLjQxIDEuNDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/sun\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sun = createLucideIcon('sun', __iconNode);\n\nexport default Sun;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAwB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACrD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtD;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "file": "arrow-right.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}