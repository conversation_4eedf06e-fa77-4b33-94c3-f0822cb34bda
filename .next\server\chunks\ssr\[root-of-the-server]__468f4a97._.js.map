{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/lib/algorithms/planning.ts"], "sourcesContent": ["import { Task, ScoredTask, DailySchedule, TimeSlot } from '@/types';\r\n\r\nexport class PlanningAlgorithm {\r\n  /**\r\n   * 计算任务的综合分数\r\n   */\r\n  calculateTaskScore(task: Task): number {\r\n    // 基础分数：重要性权重0.6，紧急性权重0.4\r\n    const baseScore = task.importance * 0.6 + task.urgency * 0.4;\r\n    \r\n    // 分类加权\r\n    const categoryBonus = {\r\n      work: 0,\r\n      improvement: 2,\r\n      entertainment: 1\r\n    }[task.category];\r\n    \r\n    // 推迟惩罚：每推迟一次扣3分\r\n    const postponePenalty = task.postponeCount * 3;\r\n    \r\n    // 截止时间紧迫性加权\r\n    const deadlineBonus = this.calculateDeadlineUrgency(task.deadline);\r\n    \r\n    return baseScore + categoryBonus + postponePenalty + deadlineBonus;\r\n  }\r\n  \r\n  /**\r\n   * 根据截止时间计算紧迫性加权\r\n   */\r\n  private calculateDeadlineUrgency(deadline: Date): number {\r\n    const now = new Date();\r\n    const hoursUntilDeadline = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);\r\n    \r\n    if (hoursUntilDeadline < 0) return 10; // 已过期，最高优先级\r\n    if (hoursUntilDeadline < 2) return 8;  // 2小时内\r\n    if (hoursUntilDeadline < 6) return 6;  // 6小时内\r\n    if (hoursUntilDeadline < 24) return 4; // 24小时内\r\n    if (hoursUntilDeadline < 72) return 2; // 3天内\r\n    return 0; // 3天以上\r\n  }\r\n  \r\n  /**\r\n   * 根据重要性和紧急性确定四象限\r\n   */\r\n  classifyQuadrant(importance: number, urgency: number): 1 | 2 | 3 | 4 {\r\n    const isImportant = importance >= 4;\r\n    const isUrgent = urgency >= 4;\r\n    \r\n    if (isImportant && isUrgent) return 1;      // 重要且紧急\r\n    if (isImportant && !isUrgent) return 2;    // 重要不紧急\r\n    if (!isImportant && isUrgent) return 3;    // 不重要但紧急\r\n    return 4;                                   // 不重要不紧急\r\n  }\r\n  \r\n  /**\r\n   * 生成今日时间安排\r\n   */\r\n  generateDailySchedule(tasks: Task[], userTimeConfig?: any): DailySchedule {\r\n    // 1. 过滤今日需要处理的任务\r\n    const todayTasks = this.filterTodayTasks(tasks);\r\n\r\n    // 2. 计算分数并分类\r\n    const scoredTasks: ScoredTask[] = todayTasks\r\n      .map(task => ({\r\n        ...task,\r\n        score: this.calculateTaskScore(task),\r\n        quadrant: this.classifyQuadrant(task.importance, task.urgency)\r\n      }))\r\n      .sort((a, b) => {\r\n        // 先按象限排序，再按分数排序\r\n        if (a.quadrant !== b.quadrant) {\r\n          return a.quadrant - b.quadrant;\r\n        }\r\n        return b.score - a.score;\r\n      });\r\n\r\n    // 3. 生成时间段（使用新的基于任务类型的算法）\r\n    const timeSlots = this.generateTimeSlotsWithCategories(scoredTasks, userTimeConfig);\r\n\r\n    // 4. 计算总时长\r\n    const totalDuration = timeSlots.reduce((sum, slot) =>\r\n      sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0\r\n    );\r\n\r\n    return {\r\n      date: new Date(),\r\n      timeSlots,\r\n      totalTasks: todayTasks.length,\r\n      estimatedDuration: totalDuration\r\n    };\r\n  }\r\n  \r\n  /**\r\n   * 过滤今日需要处理的任务\r\n   */\r\n  private filterTodayTasks(tasks: Task[]): Task[] {\r\n    const today = new Date();\r\n    const tomorrow = new Date(today);\r\n    tomorrow.setDate(tomorrow.getDate() + 1);\r\n    \r\n    return tasks.filter(task => {\r\n      // 包含今日截止的任务和未完成的高优先级任务\r\n      const isToday = task.deadline <= tomorrow;\r\n      const isHighPriority = task.importance >= 4 || task.urgency >= 4;\r\n      const isPending = task.status === 'pending' || task.status === 'in-progress';\r\n      \r\n      return isPending && (isToday || isHighPriority);\r\n    });\r\n  }\r\n  \r\n  /**\r\n   * 基于任务类型生成时间段安排\r\n   */\r\n  private generateTimeSlotsWithCategories(tasks: ScoredTask[], userTimeConfig?: any): TimeSlot[] {\r\n    const timeSlots: TimeSlot[] = [];\r\n    const today = new Date();\r\n\r\n    // 使用默认配置如果没有提供用户配置\r\n    const defaultConfig = {\r\n      workStart: '09:00',\r\n      workEnd: '18:00',\r\n      categoryPreferences: {\r\n        work: { preferredTimes: ['09:00-12:00', '14:00-18:00'], maxDaily: 480 },\r\n        improvement: { preferredTimes: ['07:00-09:00', '19:00-21:00'], maxDaily: 120 },\r\n        entertainment: { preferredTimes: ['20:00-22:00'], maxDaily: 180 }\r\n      },\r\n      fixedSlots: [\r\n        { start: '12:00', end: '13:00', type: 'meal', label: '午餐时间' }\r\n      ]\r\n    };\r\n\r\n    const config = userTimeConfig || defaultConfig;\r\n\r\n    // 按任务类型分组\r\n    const tasksByCategory = {\r\n      work: tasks.filter(task => task.category === 'work'),\r\n      improvement: tasks.filter(task => task.category === 'improvement'),\r\n      entertainment: tasks.filter(task => task.category === 'entertainment')\r\n    };\r\n\r\n    // 为每种类型的任务安排时间\r\n    for (const [category, categoryTasks] of Object.entries(tasksByCategory)) {\r\n      if (categoryTasks.length === 0) continue;\r\n\r\n      const categoryPrefs = config.categoryPreferences[category as keyof typeof config.categoryPreferences];\r\n      if (!categoryPrefs) continue;\r\n\r\n      // 为该类型任务生成可用时间段\r\n      const availableSlots = this.generateAvailableSlots(categoryPrefs.preferredTimes, config.fixedSlots, today);\r\n\r\n      // 在可用时间段中安排任务\r\n      this.scheduleTasksInSlots(categoryTasks, availableSlots, timeSlots);\r\n    }\r\n\r\n    return timeSlots.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());\r\n  }\r\n\r\n  /**\r\n   * 生成时间段安排（保留原方法作为后备）\r\n   */\r\n  private generateTimeSlots(tasks: ScoredTask[], workHours: { start: string; end: string }): TimeSlot[] {\r\n    const timeSlots: TimeSlot[] = [];\r\n    const today = new Date();\r\n\r\n    // 解析工作时间\r\n    const [startHour, startMinute] = workHours.start.split(':').map(Number);\r\n    const [endHour, endMinute] = workHours.end.split(':').map(Number);\r\n\r\n    let currentTime = new Date(today);\r\n    currentTime.setHours(startHour, startMinute, 0, 0);\r\n\r\n    const workEndTime = new Date(today);\r\n    workEndTime.setHours(endHour, endMinute, 0, 0);\r\n\r\n    for (const task of tasks) {\r\n      // 检查是否还有足够的工作时间\r\n      const remainingWorkTime = workEndTime.getTime() - currentTime.getTime();\r\n      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒\r\n\r\n      if (remainingWorkTime < taskDuration) {\r\n        // 如果当天时间不够，跳过或安排到明天\r\n        continue;\r\n      }\r\n\r\n      const endTime = new Date(currentTime.getTime() + taskDuration);\r\n\r\n      timeSlots.push({\r\n        task,\r\n        startTime: new Date(currentTime),\r\n        endTime,\r\n        isFixed: false\r\n      });\r\n\r\n      // 更新当前时间，添加15分钟休息时间\r\n      currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);\r\n\r\n      // 如果超过工作时间，停止安排\r\n      if (currentTime >= workEndTime) {\r\n        break;\r\n      }\r\n    }\r\n\r\n    return timeSlots;\r\n  }\r\n  \r\n  /**\r\n   * 获取四象限的描述\r\n   */\r\n  getQuadrantDescription(quadrant: 1 | 2 | 3 | 4): string {\r\n    const descriptions = {\r\n      1: '重要且紧急 - 立即执行',\r\n      2: '重要不紧急 - 计划执行',\r\n      3: '不重要但紧急 - 委托处理',\r\n      4: '不重要不紧急 - 减少或删除'\r\n    };\r\n    return descriptions[quadrant];\r\n  }\r\n  \r\n  /**\r\n   * 生成可用时间段\r\n   */\r\n  private generateAvailableSlots(preferredTimes: string[], fixedSlots: any[], date: Date): { start: Date; end: Date }[] {\r\n    const availableSlots: { start: Date; end: Date }[] = [];\r\n\r\n    for (const timeRange of preferredTimes) {\r\n      const [startTime, endTime] = timeRange.split('-');\r\n      const [startHour, startMinute] = startTime.split(':').map(Number);\r\n      const [endHour, endMinute] = endTime.split(':').map(Number);\r\n\r\n      const slotStart = new Date(date);\r\n      slotStart.setHours(startHour, startMinute, 0, 0);\r\n\r\n      const slotEnd = new Date(date);\r\n      slotEnd.setHours(endHour, endMinute, 0, 0);\r\n\r\n      // 检查是否与固定时间段冲突\r\n      let hasConflict = false;\r\n      for (const fixedSlot of fixedSlots) {\r\n        const [fixedStartHour, fixedStartMinute] = fixedSlot.start.split(':').map(Number);\r\n        const [fixedEndHour, fixedEndMinute] = fixedSlot.end.split(':').map(Number);\r\n\r\n        const fixedStart = new Date(date);\r\n        fixedStart.setHours(fixedStartHour, fixedStartMinute, 0, 0);\r\n\r\n        const fixedEnd = new Date(date);\r\n        fixedEnd.setHours(fixedEndHour, fixedEndMinute, 0, 0);\r\n\r\n        // 检查时间段重叠\r\n        if (slotStart < fixedEnd && slotEnd > fixedStart) {\r\n          hasConflict = true;\r\n          break;\r\n        }\r\n      }\r\n\r\n      if (!hasConflict) {\r\n        availableSlots.push({ start: slotStart, end: slotEnd });\r\n      }\r\n    }\r\n\r\n    return availableSlots;\r\n  }\r\n\r\n  /**\r\n   * 在可用时间段中安排任务\r\n   */\r\n  private scheduleTasksInSlots(tasks: ScoredTask[], availableSlots: { start: Date; end: Date }[], timeSlots: TimeSlot[]): void {\r\n    let currentSlotIndex = 0;\r\n    let currentTime = availableSlots[0]?.start;\r\n\r\n    if (!currentTime) return;\r\n\r\n    for (const task of tasks) {\r\n      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒\r\n\r\n      // 寻找合适的时间段\r\n      while (currentSlotIndex < availableSlots.length) {\r\n        const currentSlot = availableSlots[currentSlotIndex];\r\n        const remainingTime = currentSlot.end.getTime() - currentTime.getTime();\r\n\r\n        if (remainingTime >= taskDuration) {\r\n          // 在当前时间段安排任务\r\n          const endTime = new Date(currentTime.getTime() + taskDuration);\r\n\r\n          timeSlots.push({\r\n            task,\r\n            startTime: new Date(currentTime),\r\n            endTime,\r\n            isFixed: false\r\n          });\r\n\r\n          // 更新当前时间，添加15分钟休息时间\r\n          currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);\r\n          break;\r\n        } else {\r\n          // 移动到下一个时间段\r\n          currentSlotIndex++;\r\n          currentTime = availableSlots[currentSlotIndex]?.start;\r\n          if (!currentTime) break;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取任务建议\r\n   */\r\n  getTaskRecommendation(task: ScoredTask): string {\r\n    if (task.quadrant === 1) {\r\n      return '🔥 高优先级任务，建议立即处理';\r\n    } else if (task.quadrant === 2) {\r\n      return '📅 重要任务，建议合理安排时间';\r\n    } else if (task.quadrant === 3) {\r\n      return '⚡ 紧急但不重要，考虑委托或快速处理';\r\n    } else {\r\n      return '🤔 优先级较低，可以延后或删除';\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX;;GAEC,GACD,mBAAmB,IAAU,EAAU;QACrC,yBAAyB;QACzB,MAAM,YAAY,KAAK,UAAU,GAAG,MAAM,KAAK,OAAO,GAAG;QAEzD,OAAO;QACP,MAAM,gBAAgB;YACpB,MAAM;YACN,aAAa;YACb,eAAe;QACjB,CAAC,CAAC,KAAK,QAAQ,CAAC;QAEhB,gBAAgB;QAChB,MAAM,kBAAkB,KAAK,aAAa,GAAG;QAE7C,YAAY;QACZ,MAAM,gBAAgB,IAAI,CAAC,wBAAwB,CAAC,KAAK,QAAQ;QAEjE,OAAO,YAAY,gBAAgB,kBAAkB;IACvD;IAEA;;GAEC,GACD,AAAQ,yBAAyB,QAAc,EAAU;QACvD,MAAM,MAAM,IAAI;QAChB,MAAM,qBAAqB,CAAC,SAAS,OAAO,KAAK,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEjF,IAAI,qBAAqB,GAAG,OAAO,IAAI,YAAY;QACnD,IAAI,qBAAqB,GAAG,OAAO,GAAI,OAAO;QAC9C,IAAI,qBAAqB,GAAG,OAAO,GAAI,OAAO;QAC9C,IAAI,qBAAqB,IAAI,OAAO,GAAG,QAAQ;QAC/C,IAAI,qBAAqB,IAAI,OAAO,GAAG,MAAM;QAC7C,OAAO,GAAG,OAAO;IACnB;IAEA;;GAEC,GACD,iBAAiB,UAAkB,EAAE,OAAe,EAAiB;QACnE,MAAM,cAAc,cAAc;QAClC,MAAM,WAAW,WAAW;QAE5B,IAAI,eAAe,UAAU,OAAO,GAAQ,QAAQ;QACpD,IAAI,eAAe,CAAC,UAAU,OAAO,GAAM,QAAQ;QACnD,IAAI,CAAC,eAAe,UAAU,OAAO,GAAM,SAAS;QACpD,OAAO,GAAqC,SAAS;IACvD;IAEA;;GAEC,GACD,sBAAsB,KAAa,EAAE,cAAoB,EAAiB;QACxE,iBAAiB;QACjB,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC;QAEzC,aAAa;QACb,MAAM,cAA4B,WAC/B,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,GAAG,IAAI;gBACP,OAAO,IAAI,CAAC,kBAAkB,CAAC;gBAC/B,UAAU,IAAI,CAAC,gBAAgB,CAAC,KAAK,UAAU,EAAE,KAAK,OAAO;YAC/D,CAAC,GACA,IAAI,CAAC,CAAC,GAAG;YACR,gBAAgB;YAChB,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;gBAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAChC;YACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;QAC1B;QAEF,0BAA0B;QAC1B,MAAM,YAAY,IAAI,CAAC,+BAA+B,CAAC,aAAa;QAEpE,WAAW;QACX,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAC,KAAK,OAC3C,MAAM,CAAC,KAAK,OAAO,CAAC,OAAO,KAAK,KAAK,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG;QAG3E,OAAO;YACL,MAAM,IAAI;YACV;YACA,YAAY,WAAW,MAAM;YAC7B,mBAAmB;QACrB;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAiB,KAAa,EAAU;QAC9C,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QAEtC,OAAO,MAAM,MAAM,CAAC,CAAA;YAClB,uBAAuB;YACvB,MAAM,UAAU,KAAK,QAAQ,IAAI;YACjC,MAAM,iBAAiB,KAAK,UAAU,IAAI,KAAK,KAAK,OAAO,IAAI;YAC/D,MAAM,YAAY,KAAK,MAAM,KAAK,aAAa,KAAK,MAAM,KAAK;YAE/D,OAAO,aAAa,CAAC,WAAW,cAAc;QAChD;IACF;IAEA;;GAEC,GACD,AAAQ,gCAAgC,KAAmB,EAAE,cAAoB,EAAc;QAC7F,MAAM,YAAwB,EAAE;QAChC,MAAM,QAAQ,IAAI;QAElB,mBAAmB;QACnB,MAAM,gBAAgB;YACpB,WAAW;YACX,SAAS;YACT,qBAAqB;gBACnB,MAAM;oBAAE,gBAAgB;wBAAC;wBAAe;qBAAc;oBAAE,UAAU;gBAAI;gBACtE,aAAa;oBAAE,gBAAgB;wBAAC;wBAAe;qBAAc;oBAAE,UAAU;gBAAI;gBAC7E,eAAe;oBAAE,gBAAgB;wBAAC;qBAAc;oBAAE,UAAU;gBAAI;YAClE;YACA,YAAY;gBACV;oBAAE,OAAO;oBAAS,KAAK;oBAAS,MAAM;oBAAQ,OAAO;gBAAO;aAC7D;QACH;QAEA,MAAM,SAAS,kBAAkB;QAEjC,UAAU;QACV,MAAM,kBAAkB;YACtB,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YAC7C,aAAa,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YACpD,eAAe,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;QACxD;QAEA,eAAe;QACf,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,iBAAkB;YACvE,IAAI,cAAc,MAAM,KAAK,GAAG;YAEhC,MAAM,gBAAgB,OAAO,mBAAmB,CAAC,SAAoD;YACrG,IAAI,CAAC,eAAe;YAEpB,gBAAgB;YAChB,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,cAAc,cAAc,EAAE,OAAO,UAAU,EAAE;YAEpG,cAAc;YACd,IAAI,CAAC,oBAAoB,CAAC,eAAe,gBAAgB;QAC3D;QAEA,OAAO,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;IAC7E;IAEA;;GAEC,GACD,AAAQ,kBAAkB,KAAmB,EAAE,SAAyC,EAAc;QACpG,MAAM,YAAwB,EAAE;QAChC,MAAM,QAAQ,IAAI;QAElB,SAAS;QACT,MAAM,CAAC,WAAW,YAAY,GAAG,UAAU,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAChE,MAAM,CAAC,SAAS,UAAU,GAAG,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAE1D,IAAI,cAAc,IAAI,KAAK;QAC3B,YAAY,QAAQ,CAAC,WAAW,aAAa,GAAG;QAEhD,MAAM,cAAc,IAAI,KAAK;QAC7B,YAAY,QAAQ,CAAC,SAAS,WAAW,GAAG;QAE5C,KAAK,MAAM,QAAQ,MAAO;YACxB,gBAAgB;YAChB,MAAM,oBAAoB,YAAY,OAAO,KAAK,YAAY,OAAO;YACrE,MAAM,eAAe,CAAC,KAAK,iBAAiB,IAAI,EAAE,IAAI,KAAK,MAAM,QAAQ;YAEzE,IAAI,oBAAoB,cAAc;gBAEpC;YACF;YAEA,MAAM,UAAU,IAAI,KAAK,YAAY,OAAO,KAAK;YAEjD,UAAU,IAAI,CAAC;gBACb;gBACA,WAAW,IAAI,KAAK;gBACpB;gBACA,SAAS;YACX;YAEA,oBAAoB;YACpB,cAAc,IAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,KAAK;YAErD,gBAAgB;YAChB,IAAI,eAAe,aAAa;gBAC9B;YACF;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,uBAAuB,QAAuB,EAAU;QACtD,MAAM,eAAe;YACnB,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;QACL;QACA,OAAO,YAAY,CAAC,SAAS;IAC/B;IAEA;;GAEC,GACD,AAAQ,uBAAuB,cAAwB,EAAE,UAAiB,EAAE,IAAU,EAAgC;QACpH,MAAM,iBAA+C,EAAE;QAEvD,KAAK,MAAM,aAAa,eAAgB;YACtC,MAAM,CAAC,WAAW,QAAQ,GAAG,UAAU,KAAK,CAAC;YAC7C,MAAM,CAAC,WAAW,YAAY,GAAG,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC;YAC1D,MAAM,CAAC,SAAS,UAAU,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;YAEpD,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,QAAQ,CAAC,WAAW,aAAa,GAAG;YAE9C,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,QAAQ,CAAC,SAAS,WAAW,GAAG;YAExC,eAAe;YACf,IAAI,cAAc;YAClB,KAAK,MAAM,aAAa,WAAY;gBAClC,MAAM,CAAC,gBAAgB,iBAAiB,GAAG,UAAU,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;gBAC1E,MAAM,CAAC,cAAc,eAAe,GAAG,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;gBAEpE,MAAM,aAAa,IAAI,KAAK;gBAC5B,WAAW,QAAQ,CAAC,gBAAgB,kBAAkB,GAAG;gBAEzD,MAAM,WAAW,IAAI,KAAK;gBAC1B,SAAS,QAAQ,CAAC,cAAc,gBAAgB,GAAG;gBAEnD,UAAU;gBACV,IAAI,YAAY,YAAY,UAAU,YAAY;oBAChD,cAAc;oBACd;gBACF;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,eAAe,IAAI,CAAC;oBAAE,OAAO;oBAAW,KAAK;gBAAQ;YACvD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,qBAAqB,KAAmB,EAAE,cAA4C,EAAE,SAAqB,EAAQ;QAC3H,IAAI,mBAAmB;QACvB,IAAI,cAAc,cAAc,CAAC,EAAE,EAAE;QAErC,IAAI,CAAC,aAAa;QAElB,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,eAAe,CAAC,KAAK,iBAAiB,IAAI,EAAE,IAAI,KAAK,MAAM,QAAQ;YAEzE,WAAW;YACX,MAAO,mBAAmB,eAAe,MAAM,CAAE;gBAC/C,MAAM,cAAc,cAAc,CAAC,iBAAiB;gBACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC,OAAO,KAAK,YAAY,OAAO;gBAErE,IAAI,iBAAiB,cAAc;oBACjC,aAAa;oBACb,MAAM,UAAU,IAAI,KAAK,YAAY,OAAO,KAAK;oBAEjD,UAAU,IAAI,CAAC;wBACb;wBACA,WAAW,IAAI,KAAK;wBACpB;wBACA,SAAS;oBACX;oBAEA,oBAAoB;oBACpB,cAAc,IAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,KAAK;oBACrD;gBACF,OAAO;oBACL,YAAY;oBACZ;oBACA,cAAc,cAAc,CAAC,iBAAiB,EAAE;oBAChD,IAAI,CAAC,aAAa;gBACpB;YACF;QACF;IACF;IAEA;;GAEC,GACD,sBAAsB,IAAgB,EAAU;QAC9C,IAAI,KAAK,QAAQ,KAAK,GAAG;YACvB,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,GAAG;YAC9B,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,GAAG;YAC9B,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/lib/algorithms/balance.ts"], "sourcesContent": ["import { CategoryRatios, BalanceAnalysis, DailyStats } from '@/types';\r\nimport { supabase } from '@/lib/supabase';\r\n\r\nexport class BalanceAlgorithm {\r\n  // 理想的时间分配比例\r\n  private readonly IDEAL_RATIOS: CategoryRatios = {\r\n    work: 0.6,\r\n    improvement: 0.25,\r\n    entertainment: 0.15\r\n  };\r\n  \r\n  /**\r\n   * 分析用户的生活平衡状况\r\n   */\r\n  async analyzeWeeklyBalance(userId: string): Promise<BalanceAnalysis> {\r\n    try {\r\n      // 获取最近7天的统计数据\r\n      const stats = await this.getDailyStats(userId, 7);\r\n      \r\n      if (stats.length === 0) {\r\n        return {\r\n          workRatio: 0,\r\n          improvementRatio: 0,\r\n          entertainmentRatio: 0,\r\n          balanceScore: 0,\r\n          recommendation: \"开始记录你的任务来获得生活平衡分析 📊\"\r\n        };\r\n      }\r\n      \r\n      // 计算总时间和各分类时间\r\n      const totalTime = stats.reduce((sum, day) => \r\n        sum + day.workTime + day.improvementTime + day.entertainmentTime, 0\r\n      );\r\n      \r\n      if (totalTime === 0) {\r\n        return {\r\n          workRatio: 0,\r\n          improvementRatio: 0,\r\n          entertainmentRatio: 0,\r\n          balanceScore: 0,\r\n          recommendation: \"还没有完成任务记录，开始你的第一个任务吧！ 🚀\"\r\n        };\r\n      }\r\n      \r\n      // 计算各分类的时间比例\r\n      const ratios: CategoryRatios = {\r\n        work: stats.reduce((sum, day) => sum + day.workTime, 0) / totalTime,\r\n        improvement: stats.reduce((sum, day) => sum + day.improvementTime, 0) / totalTime,\r\n        entertainment: stats.reduce((sum, day) => sum + day.entertainmentTime, 0) / totalTime\r\n      };\r\n      \r\n      // 计算平衡分数\r\n      const balanceScore = this.calculateBalanceScore(ratios);\r\n      \r\n      // 生成建议\r\n      const recommendation = this.generateRecommendation(ratios, stats);\r\n      \r\n      return {\r\n        workRatio: ratios.work,\r\n        improvementRatio: ratios.improvement,\r\n        entertainmentRatio: ratios.entertainment,\r\n        balanceScore,\r\n        recommendation\r\n      };\r\n    } catch (error) {\r\n      console.error('Error analyzing balance:', error);\r\n      throw new Error('Failed to analyze balance');\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 计算生活平衡分数 (0-100)\r\n   */\r\n  private calculateBalanceScore(ratios: CategoryRatios): number {\r\n    let score = 100;\r\n    \r\n    // 计算每个分类与理想比例的偏差\r\n    Object.keys(this.IDEAL_RATIOS).forEach(category => {\r\n      const ideal = this.IDEAL_RATIOS[category as keyof CategoryRatios];\r\n      const actual = ratios[category as keyof CategoryRatios];\r\n      const deviation = Math.abs(ideal - actual);\r\n      \r\n      // 偏差越大，扣分越多\r\n      score -= deviation * 100;\r\n    });\r\n    \r\n    return Math.max(0, Math.round(score));\r\n  }\r\n  \r\n  /**\r\n   * 生成个性化建议\r\n   */\r\n  private generateRecommendation(ratios: CategoryRatios, stats: DailyStats[]): string {\r\n    const recommendations: string[] = [];\r\n    \r\n    // 分析工作时间\r\n    if (ratios.work > 0.8) {\r\n      recommendations.push(\"⚠️ 工作时间过长，建议增加休息和娱乐时间\");\r\n    } else if (ratios.work < 0.3) {\r\n      recommendations.push(\"💼 工作时间较少，可以适当增加工作或学习时间\");\r\n    }\r\n    \r\n    // 分析提升时间\r\n    if (ratios.improvement < 0.1) {\r\n      recommendations.push(\"📚 建议安排一些学习或自我提升的活动\");\r\n    } else if (ratios.improvement > 0.4) {\r\n      recommendations.push(\"🎯 学习时间充足，注意劳逸结合\");\r\n    }\r\n    \r\n    // 分析娱乐时间\r\n    if (ratios.entertainment < 0.05) {\r\n      recommendations.push(\"🎮 需要更多的放松和娱乐时间\");\r\n    } else if (ratios.entertainment > 0.3) {\r\n      recommendations.push(\"⏰ 娱乐时间较多，可以适当增加工作或学习\");\r\n    }\r\n    \r\n    // 分析连续性\r\n    const recentDays = stats.slice(-3); // 最近3天\r\n    const hasConsistentWork = recentDays.every(day => day.workTime > 0);\r\n    const hasConsistentImprovement = recentDays.every(day => day.improvementTime > 0);\r\n    \r\n    if (!hasConsistentWork) {\r\n      recommendations.push(\"🔄 建议保持每日工作的连续性\");\r\n    }\r\n    \r\n    if (!hasConsistentImprovement) {\r\n      recommendations.push(\"📈 建议每天安排一些自我提升时间\");\r\n    }\r\n    \r\n    // 如果没有特别的建议，给出正面反馈\r\n    if (recommendations.length === 0) {\r\n      const score = this.calculateBalanceScore(ratios);\r\n      if (score >= 80) {\r\n        return \"✨ 生活平衡状态优秀，继续保持！\";\r\n      } else if (score >= 60) {\r\n        return \"👍 生活平衡状态良好，可以微调优化\";\r\n      } else {\r\n        return \"🎯 生活平衡有改善空间，建议关注时间分配\";\r\n      }\r\n    }\r\n    \r\n    return recommendations.join(\" • \");\r\n  }\r\n  \r\n  /**\r\n   * 获取用户的每日统计数据\r\n   */\r\n  private async getDailyStats(userId: string, days: number): Promise<DailyStats[]> {\r\n    const startDate = new Date();\r\n    startDate.setDate(startDate.getDate() - days);\r\n    \r\n    const { data, error } = await supabase\r\n      .from('daily_stats')\r\n      .select('*')\r\n      .eq('user_id', userId)\r\n      .gte('date', startDate.toISOString().split('T')[0])\r\n      .order('date', { ascending: true });\r\n    \r\n    if (error) {\r\n      console.error('Error fetching daily stats:', error);\r\n      return [];\r\n    }\r\n    \r\n    return data.map(stat => ({\r\n      id: stat.id,\r\n      userId: stat.user_id,\r\n      date: new Date(stat.date),\r\n      workTime: stat.work_time,\r\n      improvementTime: stat.improvement_time,\r\n      entertainmentTime: stat.entertainment_time,\r\n      tasksCompleted: stat.tasks_completed,\r\n      tasksPostponed: stat.tasks_postponed,\r\n      balanceScore: stat.balance_score || 0\r\n    }));\r\n  }\r\n  \r\n  /**\r\n   * 更新今日统计数据\r\n   */\r\n  async updateTodayStats(userId: string, category: string, timeSpent: number): Promise<void> {\r\n    const today = new Date().toISOString().split('T')[0];\r\n    \r\n    try {\r\n      // 获取今日统计\r\n      const { data: existing } = await supabase\r\n        .from('daily_stats')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n        .eq('date', today)\r\n        .single();\r\n      \r\n      const updateData: any = {};\r\n      \r\n      if (category === 'work') {\r\n        updateData.work_time = (existing?.work_time || 0) + timeSpent;\r\n      } else if (category === 'improvement') {\r\n        updateData.improvement_time = (existing?.improvement_time || 0) + timeSpent;\r\n      } else if (category === 'entertainment') {\r\n        updateData.entertainment_time = (existing?.entertainment_time || 0) + timeSpent;\r\n      }\r\n      \r\n      if (existing) {\r\n        // 更新现有记录\r\n        await supabase\r\n          .from('daily_stats')\r\n          .update(updateData)\r\n          .eq('id', existing.id);\r\n      } else {\r\n        // 创建新记录\r\n        await supabase\r\n          .from('daily_stats')\r\n          .insert({\r\n            user_id: userId,\r\n            date: today,\r\n            ...updateData\r\n          });\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating daily stats:', error);\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 获取平衡状态的颜色指示\r\n   */\r\n  getBalanceStatusColor(score: number): string {\r\n    if (score >= 80) return 'text-green-600';\r\n    if (score >= 60) return 'text-yellow-600';\r\n    if (score >= 40) return 'text-orange-600';\r\n    return 'text-red-600';\r\n  }\r\n  \r\n  /**\r\n   * 获取平衡状态的描述\r\n   */\r\n  getBalanceStatusText(score: number): string {\r\n    if (score >= 80) return '优秀';\r\n    if (score >= 60) return '良好';\r\n    if (score >= 40) return '一般';\r\n    return '需要改善';\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM;IACX,YAAY;IACK,eAA+B;QAC9C,MAAM;QACN,aAAa;QACb,eAAe;IACjB,EAAE;IAEF;;GAEC,GACD,MAAM,qBAAqB,MAAc,EAA4B;QACnE,IAAI;YACF,cAAc;YACd,MAAM,QAAQ,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ;YAE/C,IAAI,MAAM,MAAM,KAAK,GAAG;gBACtB,OAAO;oBACL,WAAW;oBACX,kBAAkB;oBAClB,oBAAoB;oBACpB,cAAc;oBACd,gBAAgB;gBAClB;YACF;YAEA,cAAc;YACd,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,MACnC,MAAM,IAAI,QAAQ,GAAG,IAAI,eAAe,GAAG,IAAI,iBAAiB,EAAE;YAGpE,IAAI,cAAc,GAAG;gBACnB,OAAO;oBACL,WAAW;oBACX,kBAAkB;oBAClB,oBAAoB;oBACpB,cAAc;oBACd,gBAAgB;gBAClB;YACF;YAEA,aAAa;YACb,MAAM,SAAyB;gBAC7B,MAAM,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,QAAQ,EAAE,KAAK;gBAC1D,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,eAAe,EAAE,KAAK;gBACxE,eAAe,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,iBAAiB,EAAE,KAAK;YAC9E;YAEA,SAAS;YACT,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;YAEhD,OAAO;YACP,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,QAAQ;YAE3D,OAAO;gBACL,WAAW,OAAO,IAAI;gBACtB,kBAAkB,OAAO,WAAW;gBACpC,oBAAoB,OAAO,aAAa;gBACxC;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,MAAsB,EAAU;QAC5D,IAAI,QAAQ;QAEZ,iBAAiB;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;YACrC,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,SAAiC;YACjE,MAAM,SAAS,MAAM,CAAC,SAAiC;YACvD,MAAM,YAAY,KAAK,GAAG,CAAC,QAAQ;YAEnC,YAAY;YACZ,SAAS,YAAY;QACvB;QAEA,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC;IAChC;IAEA;;GAEC,GACD,AAAQ,uBAAuB,MAAsB,EAAE,KAAmB,EAAU;QAClF,MAAM,kBAA4B,EAAE;QAEpC,SAAS;QACT,IAAI,OAAO,IAAI,GAAG,KAAK;YACrB,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK;YAC5B,gBAAgB,IAAI,CAAC;QACvB;QAEA,SAAS;QACT,IAAI,OAAO,WAAW,GAAG,KAAK;YAC5B,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,WAAW,GAAG,KAAK;YACnC,gBAAgB,IAAI,CAAC;QACvB;QAEA,SAAS;QACT,IAAI,OAAO,aAAa,GAAG,MAAM;YAC/B,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,aAAa,GAAG,KAAK;YACrC,gBAAgB,IAAI,CAAC;QACvB;QAEA,QAAQ;QACR,MAAM,aAAa,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO;QAC3C,MAAM,oBAAoB,WAAW,KAAK,CAAC,CAAA,MAAO,IAAI,QAAQ,GAAG;QACjE,MAAM,2BAA2B,WAAW,KAAK,CAAC,CAAA,MAAO,IAAI,eAAe,GAAG;QAE/E,IAAI,CAAC,mBAAmB;YACtB,gBAAgB,IAAI,CAAC;QACvB;QAEA,IAAI,CAAC,0BAA0B;YAC7B,gBAAgB,IAAI,CAAC;QACvB;QAEA,mBAAmB;QACnB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,MAAM,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YACzC,IAAI,SAAS,IAAI;gBACf,OAAO;YACT,OAAO,IAAI,SAAS,IAAI;gBACtB,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QAEA,OAAO,gBAAgB,IAAI,CAAC;IAC9B;IAEA;;GAEC,GACD,MAAc,cAAc,MAAc,EAAE,IAAY,EAAyB;QAC/E,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,GAAG,CAAC,QAAQ,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACjD,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,IAAI,KAAK,EAAE;gBACX,QAAQ,KAAK,OAAO;gBACpB,MAAM,IAAI,KAAK,KAAK,IAAI;gBACxB,UAAU,KAAK,SAAS;gBACxB,iBAAiB,KAAK,gBAAgB;gBACtC,mBAAmB,KAAK,kBAAkB;gBAC1C,gBAAgB,KAAK,eAAe;gBACpC,gBAAgB,KAAK,eAAe;gBACpC,cAAc,KAAK,aAAa,IAAI;YACtC,CAAC;IACH;IAEA;;GAEC,GACD,MAAM,iBAAiB,MAAc,EAAE,QAAgB,EAAE,SAAiB,EAAiB;QACzF,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAEpD,IAAI;YACF,SAAS;YACT,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACtC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ,OACX,MAAM;YAET,MAAM,aAAkB,CAAC;YAEzB,IAAI,aAAa,QAAQ;gBACvB,WAAW,SAAS,GAAG,CAAC,UAAU,aAAa,CAAC,IAAI;YACtD,OAAO,IAAI,aAAa,eAAe;gBACrC,WAAW,gBAAgB,GAAG,CAAC,UAAU,oBAAoB,CAAC,IAAI;YACpE,OAAO,IAAI,aAAa,iBAAiB;gBACvC,WAAW,kBAAkB,GAAG,CAAC,UAAU,sBAAsB,CAAC,IAAI;YACxE;YAEA,IAAI,UAAU;gBACZ,SAAS;gBACT,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,eACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,SAAS,EAAE;YACzB,OAAO;gBACL,QAAQ;gBACR,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,eACL,MAAM,CAAC;oBACN,SAAS;oBACT,MAAM;oBACN,GAAG,UAAU;gBACf;YACJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA;;GAEC,GACD,sBAAsB,KAAa,EAAU;QAC3C,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,KAAa,EAAU;QAC1C,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/lib/algorithms/fix.ts"], "sourcesContent": ["import { Task, PostponedTaskAlert } from '@/types';\r\nimport { supabase } from '@/lib/supabase';\r\n\r\nexport class FixAlgorithm {\r\n  // 推迟次数阈值\r\n  private readonly POSTPONE_THRESHOLDS = {\r\n    low: 1,\r\n    medium: 3,\r\n    high: 5,\r\n    critical: 8\r\n  };\r\n  \r\n  /**\r\n   * 分析需要修复的推迟任务\r\n   */\r\n  async analyzePostponedTasks(userId: string): Promise<PostponedTaskAlert[]> {\r\n    try {\r\n      // 获取所有推迟的任务\r\n      const postponedTasks = await this.getPostponedTasks(userId);\r\n      \r\n      return postponedTasks.map(task => {\r\n        const urgencyLevel = this.calculateUrgencyLevel(task);\r\n        const suggestion = this.generateFixSuggestion(task);\r\n        const daysSinceCreated = this.calculateDaysSince(task.createdAt);\r\n        \r\n        return {\r\n          task,\r\n          postponeCount: task.postponeCount,\r\n          daysSinceCreated,\r\n          urgencyLevel,\r\n          suggestion,\r\n          shouldAlert: urgencyLevel !== 'low'\r\n        };\r\n      }).filter(alert => alert.shouldAlert); // 只返回需要提醒的任务\r\n    } catch (error) {\r\n      console.error('Error analyzing postponed tasks:', error);\r\n      return [];\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 计算任务的紧急程度\r\n   */\r\n  private calculateUrgencyLevel(task: Task): 'low' | 'medium' | 'high' | 'critical' {\r\n    const { postponeCount, deadline } = task;\r\n    const daysSinceDeadline = this.calculateDaysSince(deadline);\r\n    const daysSinceCreated = this.calculateDaysSince(task.createdAt);\r\n    \r\n    // 综合考虑推迟次数、截止时间和创建时间\r\n    let urgencyScore = 0;\r\n    \r\n    // 推迟次数评分\r\n    if (postponeCount >= this.POSTPONE_THRESHOLDS.critical) urgencyScore += 4;\r\n    else if (postponeCount >= this.POSTPONE_THRESHOLDS.high) urgencyScore += 3;\r\n    else if (postponeCount >= this.POSTPONE_THRESHOLDS.medium) urgencyScore += 2;\r\n    else if (postponeCount >= this.POSTPONE_THRESHOLDS.low) urgencyScore += 1;\r\n    \r\n    // 截止时间评分\r\n    if (daysSinceDeadline > 0) urgencyScore += 3; // 已过期\r\n    else if (daysSinceDeadline > -1) urgencyScore += 2; // 1天内到期\r\n    else if (daysSinceDeadline > -3) urgencyScore += 1; // 3天内到期\r\n    \r\n    // 创建时间评分（任务存在时间过长）\r\n    if (daysSinceCreated > 14) urgencyScore += 2; // 超过2周\r\n    else if (daysSinceCreated > 7) urgencyScore += 1; // 超过1周\r\n    \r\n    // 重要性和紧急性加权\r\n    if (task.importance >= 4) urgencyScore += 1;\r\n    if (task.urgency >= 4) urgencyScore += 1;\r\n    \r\n    // 根据总分确定紧急程度\r\n    if (urgencyScore >= 7) return 'critical';\r\n    if (urgencyScore >= 5) return 'high';\r\n    if (urgencyScore >= 3) return 'medium';\r\n    return 'low';\r\n  }\r\n  \r\n  /**\r\n   * 生成修复建议\r\n   */\r\n  private generateFixSuggestion(task: Task): string {\r\n    const urgencyLevel = this.calculateUrgencyLevel(task);\r\n    const { postponeCount, category, estimatedDuration } = task;\r\n    \r\n    // 基于紧急程度的基础建议\r\n    const baseSuggestions = {\r\n      critical: \"🚨 紧急处理：这个任务已经严重延期，建议立即处理或重新评估其必要性\",\r\n      high: \"⚠️ 重点关注：建议将任务分解为更小的部分，或调整截止时间\",\r\n      medium: \"💡 优化建议：可以设置更具体的时间安排或降低任务难度\",\r\n      low: \"📝 轻微提醒：建议适当调整任务优先级或时间安排\"\r\n    };\r\n    \r\n    let suggestion = baseSuggestions[urgencyLevel];\r\n    \r\n    // 基于推迟次数的具体建议\r\n    if (postponeCount >= 5) {\r\n      suggestion += \"\\n• 考虑将任务分解为5-10分钟的小任务\";\r\n    } else if (postponeCount >= 3) {\r\n      suggestion += \"\\n• 尝试番茄工作法，专注25分钟\";\r\n    }\r\n    \r\n    // 基于任务时长的建议\r\n    if (estimatedDuration > 120) { // 超过2小时\r\n      suggestion += \"\\n• 任务时间较长，建议分解为多个子任务\";\r\n    }\r\n    \r\n    // 基于分类的建议\r\n    if (category === 'work') {\r\n      suggestion += \"\\n• 工作任务：考虑在精力最好的时间段处理\";\r\n    } else if (category === 'improvement') {\r\n      suggestion += \"\\n• 提升任务：可以设置学习奖励机制\";\r\n    } else if (category === 'entertainment') {\r\n      suggestion += \"\\n• 娱乐任务：确保这确实是你想要的放松方式\";\r\n    }\r\n    \r\n    return suggestion;\r\n  }\r\n  \r\n  /**\r\n   * 获取推迟的任务\r\n   */\r\n  private async getPostponedTasks(userId: string): Promise<Task[]> {\r\n    const { data, error } = await supabase\r\n      .from('tasks')\r\n      .select('*')\r\n      .eq('user_id', userId)\r\n      .gt('postpone_count', 0)\r\n      .in('status', ['pending', 'postponed'])\r\n      .order('postpone_count', { ascending: false });\r\n    \r\n    if (error) {\r\n      console.error('Error fetching postponed tasks:', error);\r\n      return [];\r\n    }\r\n    \r\n    return data.map(task => ({\r\n      id: task.id,\r\n      userId: task.user_id,\r\n      title: task.title,\r\n      description: task.description,\r\n      category: task.category,\r\n      importance: task.importance,\r\n      urgency: task.urgency,\r\n      deadline: new Date(task.deadline),\r\n      estimatedDuration: task.estimated_duration,\r\n      status: task.status,\r\n      postponeCount: task.postpone_count,\r\n      createdAt: new Date(task.created_at),\r\n      updatedAt: new Date(task.updated_at)\r\n    }));\r\n  }\r\n  \r\n  /**\r\n   * 计算距离某个日期的天数\r\n   */\r\n  private calculateDaysSince(date: Date): number {\r\n    const now = new Date();\r\n    const diffTime = now.getTime() - date.getTime();\r\n    return Math.floor(diffTime / (1000 * 60 * 60 * 24));\r\n  }\r\n  \r\n  /**\r\n   * 推迟任务\r\n   */\r\n  async postponeTask(taskId: string, reason?: string): Promise<void> {\r\n    try {\r\n      // 获取当前任务\r\n      const { data: task, error: fetchError } = await supabase\r\n        .from('tasks')\r\n        .select('postpone_count')\r\n        .eq('id', taskId)\r\n        .single();\r\n      \r\n      if (fetchError) throw fetchError;\r\n      \r\n      // 更新推迟次数\r\n      const { error: updateError } = await supabase\r\n        .from('tasks')\r\n        .update({\r\n          postpone_count: (task.postpone_count || 0) + 1,\r\n          status: 'postponed',\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', taskId);\r\n      \r\n      if (updateError) throw updateError;\r\n      \r\n      // 记录推迟历史（如果有历史表的话）\r\n      // 这里可以扩展记录推迟原因等信息\r\n      \r\n    } catch (error) {\r\n      console.error('Error postponing task:', error);\r\n      throw new Error('Failed to postpone task');\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 重置任务的推迟状态\r\n   */\r\n  async resetTaskPostponeStatus(taskId: string): Promise<void> {\r\n    try {\r\n      const { error } = await supabase\r\n        .from('tasks')\r\n        .update({\r\n          postpone_count: 0,\r\n          status: 'pending',\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', taskId);\r\n      \r\n      if (error) throw error;\r\n    } catch (error) {\r\n      console.error('Error resetting task postpone status:', error);\r\n      throw new Error('Failed to reset task postpone status');\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 获取推迟统计信息\r\n   */\r\n  async getPostponeStats(userId: string): Promise<{\r\n    totalPostponedTasks: number;\r\n    averagePostponeCount: number;\r\n    mostPostponedCategory: string;\r\n  }> {\r\n    try {\r\n      const { data, error } = await supabase\r\n        .from('tasks')\r\n        .select('postpone_count, category')\r\n        .eq('user_id', userId)\r\n        .gt('postpone_count', 0);\r\n      \r\n      if (error) throw error;\r\n      \r\n      if (!data || data.length === 0) {\r\n        return {\r\n          totalPostponedTasks: 0,\r\n          averagePostponeCount: 0,\r\n          mostPostponedCategory: 'work'\r\n        };\r\n      }\r\n      \r\n      const totalPostponedTasks = data.length;\r\n      const averagePostponeCount = data.reduce((sum, task) => sum + task.postpone_count, 0) / totalPostponedTasks;\r\n      \r\n      // 统计各分类的推迟次数\r\n      const categoryStats = data.reduce((acc, task) => {\r\n        acc[task.category] = (acc[task.category] || 0) + task.postpone_count;\r\n        return acc;\r\n      }, {} as Record<string, number>);\r\n      \r\n      const mostPostponedCategory = Object.keys(categoryStats).reduce((a, b) => \r\n        categoryStats[a] > categoryStats[b] ? a : b\r\n      );\r\n      \r\n      return {\r\n        totalPostponedTasks,\r\n        averagePostponeCount: Math.round(averagePostponeCount * 10) / 10,\r\n        mostPostponedCategory\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting postpone stats:', error);\r\n      return {\r\n        totalPostponedTasks: 0,\r\n        averagePostponeCount: 0,\r\n        mostPostponedCategory: 'work'\r\n      };\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM;IACX,SAAS;IACQ,sBAAsB;QACrC,KAAK;QACL,QAAQ;QACR,MAAM;QACN,UAAU;IACZ,EAAE;IAEF;;GAEC,GACD,MAAM,sBAAsB,MAAc,EAAiC;QACzE,IAAI;YACF,YAAY;YACZ,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAEpD,OAAO,eAAe,GAAG,CAAC,CAAA;gBACxB,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;gBAChD,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC;gBAC9C,MAAM,mBAAmB,IAAI,CAAC,kBAAkB,CAAC,KAAK,SAAS;gBAE/D,OAAO;oBACL;oBACA,eAAe,KAAK,aAAa;oBACjC;oBACA;oBACA;oBACA,aAAa,iBAAiB;gBAChC;YACF,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,GAAG,aAAa;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAU,EAA0C;QAChF,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG;QACpC,MAAM,oBAAoB,IAAI,CAAC,kBAAkB,CAAC;QAClD,MAAM,mBAAmB,IAAI,CAAC,kBAAkB,CAAC,KAAK,SAAS;QAE/D,qBAAqB;QACrB,IAAI,eAAe;QAEnB,SAAS;QACT,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,gBAAgB;aACnE,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,gBAAgB;aACpE,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,gBAAgB;aACtE,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,gBAAgB;QAExE,SAAS;QACT,IAAI,oBAAoB,GAAG,gBAAgB,GAAG,MAAM;aAC/C,IAAI,oBAAoB,CAAC,GAAG,gBAAgB,GAAG,QAAQ;aACvD,IAAI,oBAAoB,CAAC,GAAG,gBAAgB,GAAG,QAAQ;QAE5D,mBAAmB;QACnB,IAAI,mBAAmB,IAAI,gBAAgB,GAAG,OAAO;aAChD,IAAI,mBAAmB,GAAG,gBAAgB,GAAG,OAAO;QAEzD,YAAY;QACZ,IAAI,KAAK,UAAU,IAAI,GAAG,gBAAgB;QAC1C,IAAI,KAAK,OAAO,IAAI,GAAG,gBAAgB;QAEvC,aAAa;QACb,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,GAAG,OAAO;QAC9B,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAU,EAAU;QAChD,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;QAChD,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG;QAEvD,cAAc;QACd,MAAM,kBAAkB;YACtB,UAAU;YACV,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,IAAI,aAAa,eAAe,CAAC,aAAa;QAE9C,cAAc;QACd,IAAI,iBAAiB,GAAG;YACtB,cAAc;QAChB,OAAO,IAAI,iBAAiB,GAAG;YAC7B,cAAc;QAChB;QAEA,YAAY;QACZ,IAAI,oBAAoB,KAAK;YAC3B,cAAc;QAChB;QAEA,UAAU;QACV,IAAI,aAAa,QAAQ;YACvB,cAAc;QAChB,OAAO,IAAI,aAAa,eAAe;YACrC,cAAc;QAChB,OAAO,IAAI,aAAa,iBAAiB;YACvC,cAAc;QAChB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,kBAAkB,MAAc,EAAmB;QAC/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,UAAU;YAAC;YAAW;SAAY,EACrC,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAM;QAE9C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,EAAE;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,IAAI,KAAK,EAAE;gBACX,QAAQ,KAAK,OAAO;gBACpB,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU;gBAC3B,SAAS,KAAK,OAAO;gBACrB,UAAU,IAAI,KAAK,KAAK,QAAQ;gBAChC,mBAAmB,KAAK,kBAAkB;gBAC1C,QAAQ,KAAK,MAAM;gBACnB,eAAe,KAAK,cAAc;gBAClC,WAAW,IAAI,KAAK,KAAK,UAAU;gBACnC,WAAW,IAAI,KAAK,KAAK,UAAU;YACrC,CAAC;IACH;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAU,EAAU;QAC7C,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,OAAO,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD;IAEA;;GAEC,GACD,MAAM,aAAa,MAAc,EAAE,MAAe,EAAiB;QACjE,IAAI;YACF,SAAS;YACT,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrD,IAAI,CAAC,SACL,MAAM,CAAC,kBACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,YAAY,MAAM;YAEtB,SAAS;YACT,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,gBAAgB,CAAC,KAAK,cAAc,IAAI,CAAC,IAAI;gBAC7C,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,aAAa,MAAM;QAEvB,mBAAmB;QACnB,kBAAkB;QAEpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,wBAAwB,MAAc,EAAiB;QAC3D,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,gBAAgB;gBAChB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,MAAc,EAIlC;QACD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,4BACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,kBAAkB;YAExB,IAAI,OAAO,MAAM;YAEjB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,OAAO;oBACL,qBAAqB;oBACrB,sBAAsB;oBACtB,uBAAuB;gBACzB;YACF;YAEA,MAAM,sBAAsB,KAAK,MAAM;YACvC,MAAM,uBAAuB,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,cAAc,EAAE,KAAK;YAExF,aAAa;YACb,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,KAAK;gBACtC,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc;gBACpE,OAAO;YACT,GAAG,CAAC;YAEJ,MAAM,wBAAwB,OAAO,IAAI,CAAC,eAAe,MAAM,CAAC,CAAC,GAAG,IAClE,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,IAAI;YAG5C,OAAO;gBACL;gBACA,sBAAsB,KAAK,KAAK,CAAC,uBAAuB,MAAM;gBAC9D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBACL,qBAAqB;gBACrB,sBAAsB;gBACtB,uBAAuB;YACzB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/store/useTaskStore.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { Task, DailySchedule, BalanceAnalysis, PostponedTaskAlert } from '@/types';\r\nimport { supabase } from '@/lib/supabase';\r\nimport { PlanningAlgorithm } from '@/lib/algorithms/planning';\r\nimport { BalanceAlgorithm } from '@/lib/algorithms/balance';\r\nimport { FixAlgorithm } from '@/lib/algorithms/fix';\r\n\r\ninterface TaskState {\r\n  // State\r\n  tasks: Task[];\r\n  dailySchedule: DailySchedule | null;\r\n  balanceAnalysis: BalanceAnalysis | null;\r\n  postponedAlerts: PostponedTaskAlert[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  \r\n  // Algorithms\r\n  planningAlgorithm: PlanningAlgorithm;\r\n  balanceAlgorithm: BalanceAlgorithm;\r\n  fixAlgorithm: FixAlgorithm;\r\n  \r\n  // Actions\r\n  fetchTasks: (userId: string) => Promise<void>;\r\n  createTask: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;\r\n  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;\r\n  deleteTask: (id: string) => Promise<void>;\r\n  completeTask: (id: string, actualDuration?: number, satisfaction?: number) => Promise<void>;\r\n  postponeTask: (id: string, reason?: string) => Promise<void>;\r\n  \r\n  // Algorithm actions\r\n  generateDailySchedule: (userId: string) => Promise<void>;\r\n  analyzeBalance: (userId: string) => Promise<void>;\r\n  analyzePostponedTasks: (userId: string) => Promise<void>;\r\n  \r\n  // Utility actions\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const useTaskStore = create<TaskState>((set, get) => ({\r\n  // Initial state\r\n  tasks: [],\r\n  dailySchedule: null,\r\n  balanceAnalysis: null,\r\n  postponedAlerts: [],\r\n  loading: false,\r\n  error: null,\r\n  \r\n  // Algorithm instances\r\n  planningAlgorithm: new PlanningAlgorithm(),\r\n  balanceAlgorithm: new BalanceAlgorithm(),\r\n  fixAlgorithm: new FixAlgorithm(),\r\n  \r\n  // Fetch tasks\r\n  fetchTasks: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { data, error } = await supabase\r\n        .from('tasks')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n        .order('created_at', { ascending: false });\r\n      \r\n      if (error) throw error;\r\n      \r\n      const tasks: Task[] = data.map(task => ({\r\n        id: task.id,\r\n        userId: task.user_id,\r\n        title: task.title,\r\n        description: task.description,\r\n        category: task.category,\r\n        importance: task.importance,\r\n        urgency: task.urgency,\r\n        deadline: new Date(task.deadline),\r\n        estimatedDuration: task.estimated_duration,\r\n        status: task.status,\r\n        postponeCount: task.postpone_count,\r\n        createdAt: new Date(task.created_at),\r\n        updatedAt: new Date(task.updated_at)\r\n      }));\r\n      \r\n      set({ tasks, loading: false });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Create task\r\n  createTask: async (taskData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { data, error } = await supabase\r\n        .from('tasks')\r\n        .insert({\r\n          user_id: taskData.userId,\r\n          title: taskData.title,\r\n          description: taskData.description,\r\n          category: taskData.category,\r\n          importance: taskData.importance,\r\n          urgency: taskData.urgency,\r\n          deadline: taskData.deadline.toISOString(),\r\n          estimated_duration: taskData.estimatedDuration,\r\n          status: taskData.status,\r\n          postpone_count: taskData.postponeCount\r\n        })\r\n        .select()\r\n        .single();\r\n      \r\n      if (error) throw error;\r\n      \r\n      const newTask: Task = {\r\n        id: data.id,\r\n        userId: data.user_id,\r\n        title: data.title,\r\n        description: data.description,\r\n        category: data.category,\r\n        importance: data.importance,\r\n        urgency: data.urgency,\r\n        deadline: new Date(data.deadline),\r\n        estimatedDuration: data.estimated_duration,\r\n        status: data.status,\r\n        postponeCount: data.postpone_count,\r\n        createdAt: new Date(data.created_at),\r\n        updatedAt: new Date(data.updated_at)\r\n      };\r\n      \r\n      set(state => ({\r\n        tasks: [newTask, ...state.tasks],\r\n        loading: false\r\n      }));\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create task';\r\n      set({ error: errorMessage, loading: false });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Update task\r\n  updateTask: async (id: string, updates: Partial<Task>) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const updateData: any = {};\r\n      if (updates.title) updateData.title = updates.title;\r\n      if (updates.description !== undefined) updateData.description = updates.description;\r\n      if (updates.category) updateData.category = updates.category;\r\n      if (updates.importance) updateData.importance = updates.importance;\r\n      if (updates.urgency) updateData.urgency = updates.urgency;\r\n      if (updates.deadline) updateData.deadline = updates.deadline.toISOString();\r\n      if (updates.estimatedDuration) updateData.estimated_duration = updates.estimatedDuration;\r\n      if (updates.status) updateData.status = updates.status;\r\n      if (updates.postponeCount !== undefined) updateData.postpone_count = updates.postponeCount;\r\n      \r\n      updateData.updated_at = new Date().toISOString();\r\n      \r\n      const { error } = await supabase\r\n        .from('tasks')\r\n        .update(updateData)\r\n        .eq('id', id);\r\n      \r\n      if (error) throw error;\r\n      \r\n      set(state => ({\r\n        tasks: state.tasks.map(task => \r\n          task.id === id ? { ...task, ...updates, updatedAt: new Date() } : task\r\n        ),\r\n        loading: false\r\n      }));\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update task';\r\n      set({ error: errorMessage, loading: false });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Delete task\r\n  deleteTask: async (id: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { error } = await supabase\r\n        .from('tasks')\r\n        .delete()\r\n        .eq('id', id);\r\n      \r\n      if (error) throw error;\r\n      \r\n      set(state => ({\r\n        tasks: state.tasks.filter(task => task.id !== id),\r\n        loading: false\r\n      }));\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete task';\r\n      set({ error: errorMessage, loading: false });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Complete task\r\n  completeTask: async (id: string, actualDuration?: number, satisfaction?: number) => {\r\n    try {\r\n      const { updateTask, balanceAlgorithm } = get();\r\n      const task = get().tasks.find(t => t.id === id);\r\n      \r\n      if (!task) throw new Error('Task not found');\r\n      \r\n      // Update task status\r\n      await updateTask(id, { status: 'completed' });\r\n      \r\n      // Record completion\r\n      if (actualDuration && satisfaction) {\r\n        await supabase\r\n          .from('task_completions')\r\n          .insert({\r\n            task_id: id,\r\n            actual_duration: actualDuration,\r\n            satisfaction_score: satisfaction\r\n          });\r\n      }\r\n      \r\n      // Update daily stats\r\n      await balanceAlgorithm.updateTodayStats(\r\n        task.userId, \r\n        task.category, \r\n        actualDuration || task.estimatedDuration\r\n      );\r\n      \r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';\r\n      set({ error: errorMessage });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Postpone task\r\n  postponeTask: async (id: string, reason?: string) => {\r\n    try {\r\n      const { fixAlgorithm } = get();\r\n      await fixAlgorithm.postponeTask(id, reason);\r\n      \r\n      // Refresh tasks\r\n      const task = get().tasks.find(t => t.id === id);\r\n      if (task) {\r\n        await get().fetchTasks(task.userId);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to postpone task';\r\n      set({ error: errorMessage });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Generate daily schedule\r\n  generateDailySchedule: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const { tasks, planningAlgorithm } = get();\r\n      const userTasks = tasks.filter(task => task.userId === userId);\r\n\r\n      // 获取用户时间配置\r\n      let userTimeConfig = null;\r\n      try {\r\n        const { data: userProfile } = await supabase\r\n          .from('user_profiles')\r\n          .select('time_config')\r\n          .eq('id', userId)\r\n          .single();\r\n\r\n        userTimeConfig = userProfile?.time_config;\r\n      } catch (error) {\r\n        console.log('No user time config found, using defaults');\r\n      }\r\n\r\n      const schedule = planningAlgorithm.generateDailySchedule(userTasks, userTimeConfig);\r\n\r\n      set({ dailySchedule: schedule, loading: false });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to generate schedule';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Analyze balance\r\n  analyzeBalance: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { balanceAlgorithm } = get();\r\n      const analysis = await balanceAlgorithm.analyzeWeeklyBalance(userId);\r\n      \r\n      set({ balanceAnalysis: analysis, loading: false });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze balance';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Analyze postponed tasks\r\n  analyzePostponedTasks: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { fixAlgorithm } = get();\r\n      const alerts = await fixAlgorithm.analyzePostponedTasks(userId);\r\n      \r\n      set({ postponedAlerts: alerts, loading: false });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze postponed tasks';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Utility actions\r\n  setLoading: (loading: boolean) => set({ loading }),\r\n  setError: (error: string | null) => set({ error }),\r\n  clearError: () => set({ error: null })\r\n}));\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AACA;AACA;;;;;;AAmCO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,gBAAgB;QAChB,OAAO,EAAE;QACT,eAAe;QACf,iBAAiB;QACjB,iBAAiB,EAAE;QACnB,SAAS;QACT,OAAO;QAEP,sBAAsB;QACtB,mBAAmB,IAAI,oIAAA,CAAA,oBAAiB;QACxC,kBAAkB,IAAI,mIAAA,CAAA,mBAAgB;QACtC,cAAc,IAAI,+HAAA,CAAA,eAAY;QAE9B,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO,MAAM;gBAEjB,MAAM,QAAgB,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACtC,IAAI,KAAK,EAAE;wBACX,QAAQ,KAAK,OAAO;wBACpB,OAAO,KAAK,KAAK;wBACjB,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,SAAS,KAAK,OAAO;wBACrB,UAAU,IAAI,KAAK,KAAK,QAAQ;wBAChC,mBAAmB,KAAK,kBAAkB;wBAC1C,QAAQ,KAAK,MAAM;wBACnB,eAAe,KAAK,cAAc;wBAClC,WAAW,IAAI,KAAK,KAAK,UAAU;wBACnC,WAAW,IAAI,KAAK,KAAK,UAAU;oBACrC,CAAC;gBAED,IAAI;oBAAE;oBAAO,SAAS;gBAAM;YAC9B,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;oBACN,SAAS,SAAS,MAAM;oBACxB,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,UAAU;oBAC/B,SAAS,SAAS,OAAO;oBACzB,UAAU,SAAS,QAAQ,CAAC,WAAW;oBACvC,oBAAoB,SAAS,iBAAiB;oBAC9C,QAAQ,SAAS,MAAM;oBACvB,gBAAgB,SAAS,aAAa;gBACxC,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO,MAAM;gBAEjB,MAAM,UAAgB;oBACpB,IAAI,KAAK,EAAE;oBACX,QAAQ,KAAK,OAAO;oBACpB,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;oBAC7B,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,SAAS,KAAK,OAAO;oBACrB,UAAU,IAAI,KAAK,KAAK,QAAQ;oBAChC,mBAAmB,KAAK,kBAAkB;oBAC1C,QAAQ,KAAK,MAAM;oBACnB,eAAe,KAAK,cAAc;oBAClC,WAAW,IAAI,KAAK,KAAK,UAAU;oBACnC,WAAW,IAAI,KAAK,KAAK,UAAU;gBACrC;gBAEA,IAAI,CAAA,QAAS,CAAC;wBACZ,OAAO;4BAAC;+BAAY,MAAM,KAAK;yBAAC;wBAChC,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,cAAc;QACd,YAAY,OAAO,IAAY;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,aAAkB,CAAC;gBACzB,IAAI,QAAQ,KAAK,EAAE,WAAW,KAAK,GAAG,QAAQ,KAAK;gBACnD,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;gBACnF,IAAI,QAAQ,QAAQ,EAAE,WAAW,QAAQ,GAAG,QAAQ,QAAQ;gBAC5D,IAAI,QAAQ,UAAU,EAAE,WAAW,UAAU,GAAG,QAAQ,UAAU;gBAClE,IAAI,QAAQ,OAAO,EAAE,WAAW,OAAO,GAAG,QAAQ,OAAO;gBACzD,IAAI,QAAQ,QAAQ,EAAE,WAAW,QAAQ,GAAG,QAAQ,QAAQ,CAAC,WAAW;gBACxE,IAAI,QAAQ,iBAAiB,EAAE,WAAW,kBAAkB,GAAG,QAAQ,iBAAiB;gBACxF,IAAI,QAAQ,MAAM,EAAE,WAAW,MAAM,GAAG,QAAQ,MAAM;gBACtD,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,cAAc,GAAG,QAAQ,aAAa;gBAE1F,WAAW,UAAU,GAAG,IAAI,OAAO,WAAW;gBAE9C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO,MAAM;gBAEjB,IAAI,CAAA,QAAS,CAAC;wBACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,KAAK;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;wBAEpE,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO,MAAM;gBAEjB,IAAI,CAAA,QAAS,CAAC;wBACZ,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;wBAC9C,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,gBAAgB;QAChB,cAAc,OAAO,IAAY,gBAAyB;YACxD,IAAI;gBACF,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG;gBACzC,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAE5C,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;gBAE3B,qBAAqB;gBACrB,MAAM,WAAW,IAAI;oBAAE,QAAQ;gBAAY;gBAE3C,oBAAoB;gBACpB,IAAI,kBAAkB,cAAc;oBAClC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,oBACL,MAAM,CAAC;wBACN,SAAS;wBACT,iBAAiB;wBACjB,oBAAoB;oBACtB;gBACJ;gBAEA,qBAAqB;gBACrB,MAAM,iBAAiB,gBAAgB,CACrC,KAAK,MAAM,EACX,KAAK,QAAQ,EACb,kBAAkB,KAAK,iBAAiB;YAG5C,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,gBAAgB;QAChB,cAAc,OAAO,IAAY;YAC/B,IAAI;gBACF,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,MAAM,aAAa,YAAY,CAAC,IAAI;gBAEpC,gBAAgB;gBAChB,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC5C,IAAI,MAAM;oBACR,MAAM,MAAM,UAAU,CAAC,KAAK,MAAM;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,0BAA0B;QAC1B,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG;gBACrC,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;gBAEvD,WAAW;gBACX,IAAI,iBAAiB;gBACrB,IAAI;oBACF,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACzC,IAAI,CAAC,iBACL,MAAM,CAAC,eACP,EAAE,CAAC,MAAM,QACT,MAAM;oBAET,iBAAiB,aAAa;gBAChC,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC;gBACd;gBAEA,MAAM,WAAW,kBAAkB,qBAAqB,CAAC,WAAW;gBAEpE,IAAI;oBAAE,eAAe;oBAAU,SAAS;gBAAM;YAChD,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,kBAAkB;QAClB,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,gBAAgB,EAAE,GAAG;gBAC7B,MAAM,WAAW,MAAM,iBAAiB,oBAAoB,CAAC;gBAE7D,IAAI;oBAAE,iBAAiB;oBAAU,SAAS;gBAAM;YAClD,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,0BAA0B;QAC1B,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,MAAM,SAAS,MAAM,aAAa,qBAAqB,CAAC;gBAExD,IAAI;oBAAE,iBAAiB;oBAAQ,SAAS;gBAAM;YAChD,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,kBAAkB;QAClB,YAAY,CAAC,UAAqB,IAAI;gBAAE;YAAQ;QAChD,UAAU,CAAC,QAAyB,IAAI;gBAAE;YAAM;QAChD,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC", "debugId": null}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/components/QuadrantSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface QuadrantSelectorProps {\n  importance: number;\n  urgency: number;\n  onChange: (importance: number, urgency: number) => void;\n}\n\nexport default function QuadrantSelector({ importance, urgency, onChange }: QuadrantSelectorProps) {\n  const [selectedQuadrant, setSelectedQuadrant] = useState<number | null>(null);\n\n  // 根据重要性和紧急性确定象限\n  const getQuadrant = (imp: number, urg: number): number => {\n    if (imp >= 4 && urg >= 4) return 1; // 重要且紧急\n    if (imp >= 4 && urg < 4) return 2;  // 重要不紧急\n    if (imp < 4 && urg >= 4) return 3;  // 不重要但紧急\n    return 4; // 不重要不紧急\n  };\n\n  const currentQuadrant = getQuadrant(importance, urgency);\n\n  // 象限配置\n  const quadrants = [\n    {\n      id: 1,\n      title: '重要且紧急',\n      description: '立即处理',\n      color: 'bg-red-100 border-red-300 hover:bg-red-200',\n      selectedColor: 'bg-red-200 border-red-500',\n      textColor: 'text-red-800',\n      importance: 5,\n      urgency: 5,\n      position: 'top-right'\n    },\n    {\n      id: 2,\n      title: '重要不紧急',\n      description: '计划安排',\n      color: 'bg-blue-100 border-blue-300 hover:bg-blue-200',\n      selectedColor: 'bg-blue-200 border-blue-500',\n      textColor: 'text-blue-800',\n      importance: 5,\n      urgency: 2,\n      position: 'top-left'\n    },\n    {\n      id: 3,\n      title: '不重要但紧急',\n      description: '委托处理',\n      color: 'bg-yellow-100 border-yellow-300 hover:bg-yellow-200',\n      selectedColor: 'bg-yellow-200 border-yellow-500',\n      textColor: 'text-yellow-800',\n      importance: 2,\n      urgency: 5,\n      position: 'bottom-right'\n    },\n    {\n      id: 4,\n      title: '不重要不紧急',\n      description: '减少或删除',\n      color: 'bg-gray-100 border-gray-300 hover:bg-gray-200',\n      selectedColor: 'bg-gray-200 border-gray-500',\n      textColor: 'text-gray-800',\n      importance: 2,\n      urgency: 2,\n      position: 'bottom-left'\n    }\n  ];\n\n  const handleQuadrantClick = (quadrant: typeof quadrants[0]) => {\n    setSelectedQuadrant(quadrant.id);\n    onChange(quadrant.importance, quadrant.urgency);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"text-sm font-medium text-gray-700 mb-2\">\n        任务优先级（点击象限选择）\n      </div>\n      \n      {/* 四象限网格 */}\n      <div className=\"relative w-full max-w-md mx-auto\">\n        {/* 坐标轴标签 */}\n        <div className=\"absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 font-medium\">\n          紧急程度 →\n        </div>\n        <div className=\"absolute -left-12 top-1/2 transform -translate-y-1/2 -rotate-90 text-xs text-gray-500 font-medium\">\n          重要程度 →\n        </div>\n        \n        {/* 象限网格 */}\n        <div className=\"grid grid-cols-2 gap-2 w-80 h-80\">\n          {/* 第二象限：重要不紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 2 ? quadrants[1].selectedColor : quadrants[1].color}\n              ${quadrants[1].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[1])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">重要</div>\n            <div className=\"font-semibold text-sm mb-2\">不紧急</div>\n            <div className=\"text-xs opacity-75\">计划安排</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 II</div>\n          </div>\n\n          {/* 第一象限：重要且紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 1 ? quadrants[0].selectedColor : quadrants[0].color}\n              ${quadrants[0].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[0])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">重要</div>\n            <div className=\"font-semibold text-sm mb-2\">紧急</div>\n            <div className=\"text-xs opacity-75\">立即处理</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 I</div>\n          </div>\n\n          {/* 第四象限：不重要不紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 4 ? quadrants[3].selectedColor : quadrants[3].color}\n              ${quadrants[3].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[3])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">不重要</div>\n            <div className=\"font-semibold text-sm mb-2\">不紧急</div>\n            <div className=\"text-xs opacity-75\">减少删除</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 IV</div>\n          </div>\n\n          {/* 第三象限：不重要但紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 3 ? quadrants[2].selectedColor : quadrants[2].color}\n              ${quadrants[2].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[2])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">不重要</div>\n            <div className=\"font-semibold text-sm mb-2\">紧急</div>\n            <div className=\"text-xs opacity-75\">委托处理</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 III</div>\n          </div>\n        </div>\n\n        {/* 当前选择显示 */}\n        <div className=\"mt-4 text-center\">\n          <div className=\"text-sm text-gray-600\">\n            当前选择：<span className=\"font-medium\">象限 {currentQuadrant}</span>\n          </div>\n          <div className=\"text-xs text-gray-500 mt-1\">\n            重要性: {importance}/5 | 紧急性: {urgency}/5\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,iBAAiB,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAyB;IAC/F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,gBAAgB;IAChB,MAAM,cAAc,CAAC,KAAa;QAChC,IAAI,OAAO,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;QAC5C,IAAI,OAAO,KAAK,MAAM,GAAG,OAAO,GAAI,QAAQ;QAC5C,IAAI,MAAM,KAAK,OAAO,GAAG,OAAO,GAAI,SAAS;QAC7C,OAAO,GAAG,SAAS;IACrB;IAEA,MAAM,kBAAkB,YAAY,YAAY;IAEhD,OAAO;IACP,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;KACD;IAED,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB,SAAS,EAAE;QAC/B,SAAS,SAAS,UAAU,EAAE,SAAS,OAAO;IAChD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAAyC;;;;;;0BAKxD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCAAwF;;;;;;kCAGvG,8OAAC;wBAAI,WAAU;kCAAoG;;;;;;kCAKnH,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAI1C,8OAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAI1C,8OAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAI1C,8OAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAK5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAwB;kDAChC,8OAAC;wCAAK,WAAU;;4CAAc;4CAAI;;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;oCAA6B;oCACpC;oCAAW;oCAAW;oCAAQ;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD", "debugId": null}}, {"offset": {"line": 1386, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/app/tasks/new/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuthStore } from '@/store/useAuthStore';\r\nimport { useTaskStore } from '@/store/useTaskStore';\r\nimport { ArrowLeft, Save } from 'lucide-react';\r\nimport QuadrantSelector from '@/components/QuadrantSelector';\r\n\r\nexport default function NewTask() {\r\n  const router = useRouter();\r\n  const { user } = useAuthStore();\r\n  const { createTask, loading } = useTaskStore();\r\n  \r\n  const [formData, setFormData] = useState({\r\n    title: '',\r\n    category: 'work' as 'work' | 'improvement' | 'entertainment',\r\n    importance: 3,\r\n    urgency: 3,\r\n    deadline: '',\r\n    estimatedDuration: 1 // 改为小时单位\r\n  });\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!user) {\r\n      router.push('/auth/signin');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await createTask({\r\n        userId: user.id,\r\n        title: formData.title,\r\n        description: '', // 移除description字段，设为空字符串\r\n        category: formData.category,\r\n        importance: formData.importance as 1 | 2 | 3 | 4 | 5,\r\n        urgency: formData.urgency as 1 | 2 | 3 | 4 | 5,\r\n        deadline: new Date(formData.deadline),\r\n        estimatedDuration: formData.estimatedDuration * 60, // 转换为分钟\r\n        status: 'pending',\r\n        postponeCount: 0\r\n      });\r\n      \r\n      router.push('/dashboard');\r\n    } catch (error) {\r\n      console.error('Failed to create task:', error);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value, type } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'number' ? parseInt(value) : value\r\n    }));\r\n  };\r\n\r\n  // 处理四象限选择器的变化\r\n  const handleQuadrantChange = (importance: number, urgency: number) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      importance,\r\n      urgency\r\n    }));\r\n  };\r\n\r\n  // 设置默认截止时间为明天\r\n  const getDefaultDeadline = () => {\r\n    const tomorrow = new Date();\r\n    tomorrow.setDate(tomorrow.getDate() + 1);\r\n    tomorrow.setHours(18, 0, 0, 0); // 默认下午6点\r\n    return tomorrow.toISOString().slice(0, 16); // YYYY-MM-DDTHH:MM format\r\n  };\r\n\r\n  if (!formData.deadline) {\r\n    setFormData(prev => ({ ...prev, deadline: getDefaultDeadline() }));\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Header */}\r\n      <header className=\"bg-white shadow-sm border-b\">\r\n        <div className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex items-center h-16\">\r\n            <button\r\n              onClick={() => router.back()}\r\n              className=\"flex items-center text-gray-600 hover:text-gray-900 mr-4\"\r\n            >\r\n              <ArrowLeft className=\"h-5 w-5 mr-1\" />\r\n              返回\r\n            </button>\r\n            <h1 className=\"text-xl font-semibold text-gray-900\">创建新任务</h1>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main Content */}\r\n      <main className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <div className=\"bg-white rounded-lg shadow p-6\">\r\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n            {/* 任务标题 */}\r\n            <div>\r\n              <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                任务标题 *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"title\"\r\n                name=\"title\"\r\n                required\r\n                value={formData.title}\r\n                onChange={handleChange}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                placeholder=\"请输入任务标题\"\r\n              />\r\n            </div>\r\n\r\n            {/* 四象限优先级选择器 */}\r\n            <div>\r\n              <QuadrantSelector\r\n                importance={formData.importance}\r\n                urgency={formData.urgency}\r\n                onChange={handleQuadrantChange}\r\n              />\r\n            </div>\r\n\r\n            {/* 任务分类 */}\r\n            <div>\r\n              <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                任务分类 *\r\n              </label>\r\n              <select\r\n                id=\"category\"\r\n                name=\"category\"\r\n                required\r\n                value={formData.category}\r\n                onChange={handleChange}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n              >\r\n                <option value=\"work\">工作</option>\r\n                <option value=\"improvement\">提升</option>\r\n                <option value=\"entertainment\">娱乐</option>\r\n              </select>\r\n              <p className=\"mt-1 text-sm text-gray-500\">\r\n                工作：日常工作任务 | 提升：学习和自我提升 | 娱乐：休闲和放松\r\n              </p>\r\n            </div>\r\n\r\n\r\n\r\n            {/* 截止时间和预估时长 */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div>\r\n                <label htmlFor=\"deadline\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  截止时间 *\r\n                </label>\r\n                <input\r\n                  type=\"datetime-local\"\r\n                  id=\"deadline\"\r\n                  name=\"deadline\"\r\n                  required\r\n                  value={formData.deadline}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label htmlFor=\"estimatedDuration\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  预估时长（小时）*\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  id=\"estimatedDuration\"\r\n                  name=\"estimatedDuration\"\r\n                  required\r\n                  min=\"0.25\"\r\n                  max=\"8\"\r\n                  step=\"0.25\"\r\n                  value={formData.estimatedDuration}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                />\r\n                <p className=\"mt-1 text-sm text-gray-500\">建议：0.25-8小时（15分钟-8小时）</p>\r\n              </div>\r\n            </div>\r\n\r\n\r\n\r\n            {/* 提交按钮 */}\r\n            <div className=\"flex justify-end space-x-3\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => router.back()}\r\n                className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n              >\r\n                取消\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className=\"flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? (\r\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                ) : (\r\n                  <Save className=\"h-4 w-4 mr-2\" />\r\n                )}\r\n                创建任务\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,YAAY;QACZ,SAAS;QACT,UAAU;QACV,mBAAmB,EAAE,SAAS;IAChC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,WAAW;gBACf,QAAQ,KAAK,EAAE;gBACf,OAAO,SAAS,KAAK;gBACrB,aAAa;gBACb,UAAU,SAAS,QAAQ;gBAC3B,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,UAAU,IAAI,KAAK,SAAS,QAAQ;gBACpC,mBAAmB,SAAS,iBAAiB,GAAG;gBAChD,QAAQ;gBACR,eAAe;YACjB;YAEA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,WAAW,SAAS,SAAS;YAChD,CAAC;IACH;IAEA,cAAc;IACd,MAAM,uBAAuB,CAAC,YAAoB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP;gBACA;YACF,CAAC;IACH;IAEA,cAAc;IACd,MAAM,qBAAqB;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QACtC,SAAS,QAAQ,CAAC,IAAI,GAAG,GAAG,IAAI,SAAS;QACzC,OAAO,SAAS,WAAW,GAAG,KAAK,CAAC,GAAG,KAAK,0BAA0B;IACxE;IAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAqB,CAAC;IAClE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI;gCAC1B,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAA+C;;;;;;kDAGhF,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,KAAK;wCACrB,UAAU;wCACV,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;0CACC,cAAA,8OAAC,sIAAA,CAAA,UAAgB;oCACf,YAAY,SAAS,UAAU;oCAC/B,SAAS,SAAS,OAAO;oCACzB,UAAU;;;;;;;;;;;0CAKd,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAA+C;;;;;;kDAGnF,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,8OAAC;gDAAO,OAAM;0DAAgB;;;;;;;;;;;;kDAEhC,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAQ5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA+C;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAId,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAoB,WAAU;0DAA+C;;;;;;0DAG5F,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,QAAQ;gDACR,KAAI;gDACJ,KAAI;gDACJ,MAAK;gDACL,OAAO,SAAS,iBAAiB;gDACjC,UAAU;gDACV,WAAU;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAO9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;;4CAET,wBACC,8OAAC;gDAAI,WAAU;;;;;qEAEf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 1790, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].AppRouterContext\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1797, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].HooksClientContext\n"], "names": ["module", "exports", "require", "vendored", "HooksClientContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1804, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/router-reducer/reducers/get-segment-value.ts"], "sourcesContent": ["import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n"], "names": ["getSegmentValue", "segment", "Array", "isArray"], "mappings": ";;;;+BAEgBA,mBAAAA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,OAAgB;IAC9C,OAAOC,MAAMC,OAAO,CAACF,WAAWA,OAAO,CAAC,EAAE,GAAGA;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;;IA4BaA,mBAAmB,EAAA;eAAnBA;;IADAC,gBAAgB,EAAA;eAAhBA;;IAhBGC,4BAA4B,EAAA;eAA5BA;;IATAC,cAAc,EAAA;eAAdA;;IAKAC,sBAAsB,EAAA;eAAtBA;;;AALT,SAASD,eAAeE,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASF,uBAAuBC,OAAe;IACpD,OAAOA,QAAQE,UAAU,CAAC,QAAQF,YAAY;AAChD;AAEO,SAASH,6BACdG,OAAgB,EAChBG,YAA2D;IAE3D,MAAMC,gBAAgBJ,QAAQK,QAAQ,CAACT;IAEvC,IAAIQ,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBV,mBAAmB,MAAMU,mBACzBV;IACN;IAEA,OAAOI;AACT;AAEO,MAAMJ,mBAAmB;AACzB,MAAMD,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1885, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;;+BAAYA,sBAAAA;;;eAAAA;;;AAAL,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1913, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "RedirectStatusCode"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,mBAAmB,EAAA;eAAnBA;;IAEDC,YAAY,EAAA;eAAZA;;IAgBIC,eAAe,EAAA;eAAfA;;;oCApBmB;AAE5B,MAAMF,sBAAsB;AAE5B,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;;AAgBL,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcG,oBAAAA,kBAAkB;AAEpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1969, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "actionAsyncStorage", "window", "require", "undefined", "url", "type", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "error", "Error", "REDIRECT_ERROR_CODE", "digest", "getStore", "isAction", "RedirectType", "push", "replace", "PermanentRedirect", "isRedirectError", "split", "slice", "join", "Number", "at"], "mappings": ";;;;;;;;;;;;;;;;;;;IAegBA,gBAAgB,EAAA;eAAhBA;;IA6EAC,8BAA8B,EAAA;eAA9BA;;IARAC,wBAAwB,EAAA;eAAxBA;;IARAC,uBAAuB,EAAA;eAAvBA;;IAhBAC,iBAAiB,EAAA;eAAjBA;;IAvBAC,QAAQ,EAAA;eAARA;;;oCArCmB;+BAM5B;AAEP,MAAMC,qBACJ,OAAOC,WAAW,cAEZC,QAAQ,2KACRF,kBAAkB,GACpBG;AAEC,SAAST,iBACdU,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,aAAiCC,oBAAAA,kBAAkB,CAACC,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,MAAMC,eAAAA,mBAAmB,GAA7B,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CF,MAAMG,MAAM,GAAMD,eAAAA,mBAAmB,GAAC,MAAGN,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOG;AACT;AAcO,SAASV,SACd,2BAA2B,GAC3BK,GAAW,EACXC,IAAmB;QAEVL;IAATK,QAAAA,OAAAA,OAAAA,OAASL,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,+BAAAA,mBAAoBa,QAAQ,EAAA,KAAA,OAAA,KAAA,IAA5Bb,6BAAgCc,QAAQ,IAC7CC,eAAAA,YAAY,CAACC,IAAI,GACjBD,eAAAA,YAAY,CAACE,OAAO;IAExB,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACC,iBAAiB;AACxE;AAaO,SAASV,kBACd,2BAA2B,GAC3BM,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,OAAqBU,eAAAA,YAAY,CAACE,OAAO;IAEzC,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACW,iBAAiB;AACxE;AAUO,SAASrB,wBAAwBY,KAAc;IACpD,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAAS1B,yBAAyBa,KAAoB;IAC3D,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASzB,+BAA+Bc,KAAoB;IACjE,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOa,OAAOd,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKI,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2067, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status"], "mappings": ";;;;;;;;;;;;;;;;;;IAAaA,qBAAqB,EAAA;eAArBA;;IAQAC,8BAA8B,EAAA;eAA9BA;;IAuCGC,kCAAkC,EAAA;eAAlCA;;IAPAC,2BAA2B,EAAA;eAA3BA;;IAnBAC,yBAAyB,EAAA;eAAzBA;;;AArBT,MAAMJ,wBAAwB;IACnCK,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB;AAEA,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX;AAErC,MAAMC,iCAAiC;AAavC,SAASG,0BACdQ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWb,kCACXO,cAAcS,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASZ,4BACdS,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASb,mCACdiB,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2143, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["notFound", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "error", "Error", "digest"], "mappings": ";;;;+BAsBgBA,YAAAA;;;eAAAA;;;oCAnBT;AAEP;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,4CAA4C;IAC5C,MAAMG,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2190, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["forbidden", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAqBgBA,aAAAA;;;eAAAA;;;oCAlBT;AAEP,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2243, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["unauthorized", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAsBgBA,gBAAAA;;;eAAAA;;;oCAnBT;AAEP,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2297, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2337, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;;+BAWgBA,qBAAAA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,CAAAA,GAAAA,eAAAA,eAAe,EAACD,UAAUE,CAAAA,GAAAA,oBAAAA,yBAAyB,EAACF;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2364, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/unstable-rethrow.browser.ts"], "sourcesContent": ["import { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (isNextRouterError(error) || isBailoutToCSRError(error)) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["unstable_rethrow", "error", "isNextRouterError", "isBailoutToCSRError", "Error", "cause"], "mappings": ";;;;+BAGgBA,oBAAAA;;;eAAAA;;;8BAHoB;mCACF;AAE3B,SAASA,iBAAiBC,KAAc;IAC7C,IAAIC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,UAAUE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,QAAQ;QAC1D,MAAMA;IACR;IAEA,IAAIA,iBAAiBG,SAAS,WAAWH,OAAO;QAC9CD,iBAAiBC,MAAMI,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IAgCAC,kBAAkB,EAAA;eAAlBA;;;AAhCT,SAASD,+BACdE,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAAST,mBACdU,MAAmB,EACnBH,UAAkB;IAElB,IAAIG,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIT,6BAA6BG;IACzD,OAAO;QACL,MAAMO,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIb,6BAA6BG;YAEnC,IAAIW,mBAAmBV,uBAAuBW,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCR,uBAAuBc,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2467, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2486, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "mappings": ";;;;;;;;;;;;;;;IAEaA,kBAAkB,EAAA;eAAlBA;;IAQGC,oBAAoB,EAAA;eAApBA;;;AAVhB,MAAMC,qBAAqB;AAEpB,MAAMF,2BAA2BG;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCJ;IAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2532, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "code", "error"], "mappings": ";;;;;;;;;;;;;;;IAEaA,qBAAqB,EAAA;eAArBA;;IAIGC,uBAAuB,EAAA;eAAvBA;;;AANhB,MAAMC,0BAA0B;AAEzB,MAAMF,8BAA8BG;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOF;;AACzB;AAEO,SAASD,wBACdI,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMD,IAAI,KAAKF;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2578, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2612, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": ";;;;;;;;;;;;;;;;;IA4CgBA,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAAWE;IAC3C,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;QAEzC,OAAO;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAAWG;IAC1C,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2679, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "then", "scheduleOnNextTick", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoVeA,QAAQ,EAAA;eAARA;;IA3CAC,2CAA2C,EAAA;eAA3CA;;IAlCAC,kCAAkC,EAAA;eAAlCA;;IAuKAC,mBAAmB,EAAA;eAAnBA;;IA4GAC,qBAAqB,EAAA;eAArBA;;IAtGAC,oBAAoB,EAAA;eAApBA;;IAhXAC,0BAA0B,EAAA;eAA1BA;;IAWAC,4BAA4B,EAAA;eAA5BA;;IAmbAC,6BAA6B,EAAA;eAA7BA;;IAjBAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IAtWAC,qBAAqB,EAAA;eAArBA;;IAgSAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IA3TAC,yBAAyB,EAAA;eAAzBA;;IAuPAC,oBAAoB,EAAA;eAApBA;;IAgSAC,wBAAwB,EAAA;eAAxBA;;IAvcAC,gCAAgC,EAAA;eAAhCA;;IA6ZAC,yBAAyB,EAAA;eAAzBA;;IApYAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAiHAC,qCAAqC,EAAA;eAArCA;;IAmDHC,sCAAsC,EAAA;eAAtCA;;IA+NGC,qBAAqB,EAAA;eAArBA;;;8DA9hBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASpB,2BACdqB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASxB;IACd,OAAO;QACLyB,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASzB,sBACd0B,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASxB,0BACdyB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1C1B,qBACEwB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASrC,2BACdmB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhE1B,qBAAqBwB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS9B,iCACdqB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS9B,gCACd0C,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASpC,mCACd4C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;QAC9C;IACF;IACAR,oCAAoChB,OAAOR,YAAYoB;AACzD;AAEO,SAASrC,sCACdkD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASvE,4CACd6C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMe,kBAAkBf,eAAeQ,UAAU,CAACQ,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1B,MAAM5B,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBlB,qBAAqB,GAAGS;gBACxCS,gBAAgBhB,yBAAyB,GAAGuC;gBAC5C,IAAIZ,eAAekB,UAAU,KAAK,MAAM;oBACtC,2EAA2E;oBAC3E,sEAAsE;oBACtE7B,gBAAgB8B,iBAAiB,GAAG;gBACtC;YACF;QACF;QACAf,oCAAoChB,OAAOR,YAAYoB;IACzD;IACA,MAAMO,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMhB,yCACXD;AASK,SAASrB,SAAS,EAAE+D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACNhC,qBAAqB+B,OAAOiB,QAAQhB;AACtC;AAEO,SAAShC,qBACd+B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C+B;IACA,IAAI/B,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;IAEAb,OAAAA,OAAK,CAACC,iBAAiB,CAACqD,qBAAqBjC,OAAOR;AACtD;AAEA,SAASyC,qBAAqBjC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS1B,kBAAkBqC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY+B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBhC,IAAY+B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBlB,MAAc;IAC7C,OACEA,OAAOmB,QAAQ,CACb,sEAEFnB,OAAOmB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIV,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMc,6BAA6B;AAEnC,SAASlB,gCAAgCe,OAAe;IACtD,MAAMhB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BhB,MAAcoB,MAAM,GAAGD;IACzB,OAAOnB;AACT;AAMO,SAASnD,4BACdmD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcoB,MAAM,KAAKD,8BAC1B,UAAUnB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASlE,oBACdyB,eAAqC;IAErC,OAAOA,gBAAgByD,MAAM,GAAG;AAClC;AAEO,SAAShF,qBACdiF,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAc1D,eAAe,CAACwC,IAAI,IAAImB,cAAc3D,eAAe;IACnE,OAAO0D,cAAc1D,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJ4D,MAAM,CACL,CAACC,SACC,OAAOA,OAAOpC,KAAK,KAAK,YAAYoC,OAAOpC,KAAK,CAACgC,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEpD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLsC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAExD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASyB;IACP,IAAI,CAACtD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI6C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAAS5D,2BAA2BsD,MAAc;IACvDe;IACA,MAAMZ,aAAa,IAAI6B;IACvB,qFAAqF;IACrF,IAAI;QACFtE,OAAAA,OAAK,CAACC,iBAAiB,CAACqC;IAC1B,EAAE,OAAOiC,GAAY;QACnB9B,WAAWC,KAAK,CAAC6B;IACnB;IACA,OAAO9B,WAAWQ,MAAM;AAC1B;AAOO,SAASlE,8BACdgC,aAAmC;IAEnC,MAAM0B,aAAa,IAAI6B;IAEvB,IAAIvD,cAAcyD,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCzD,cAAcyD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1CjC,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1DiC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMlC,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWQ,MAAM;AAC1B;AAEO,SAAStE,sBACdkC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnCf,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASf,sBAAsBe,UAAkB;IACtD,MAAM+D,YAAYC,0BAAAA,gBAAgB,CAAC1C,QAAQ;IAE3C,IACEyC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAMjE,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;QACnD,IAAIpB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;gBACtC,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvDhB,OAAAA,OAAK,CAACiF,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACnE,cAAcoE,YAAY,EAAEtE;YAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9B1B,qBACEsF,UAAUvD,KAAK,EACfR,YACAE,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDxB,iCAAiCqB,YAAY+D,WAAW7D;YAC1D;QACF;IACF;AACF;AAEA,MAAMqE,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAASlG,0BACd4B,KAAa,EACbuE,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAI4B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBrF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIgF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBpF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,iBAAiBU,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBtF,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLsD,cAAcvD,yBAAyB,IACvCwD,cAAcxD,yBAAyB,EACvC;QACAuF,kBAAkBnF,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAM6C,UAAU,CAAC,OAAO,EAAElC,MAAM,+UAA+U,CAAC;QAChX,MAAMkB,QAAQwD,8BAA8BxC,SAASqC;QACrDC,kBAAkBlF,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASwD,8BACPxC,OAAe,EACfqC,cAAsB;IAEtB,MAAMrD,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BhB,MAAMX,KAAK,GAAG,YAAY2B,UAAUqC;IACpC,OAAOrD;AACT;AAEO,SAAShD,yBACd8B,KAAa,EACbwE,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAIkC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIrC,cAAcvD,yBAAyB,EAAE;QAC3C0F,YAAYnC,cAAcvD,yBAAyB;QACnD2F,iBAAiBpC,cAAczD,qBAAqB;QACpD8F,aAAarC,cAAcT,iBAAiB,KAAK;IACnD,OAAO,IAAIU,cAAcxD,yBAAyB,EAAE;QAClD0F,YAAYlC,cAAcxD,yBAAyB;QACnD2F,iBAAiBnC,cAAc1D,qBAAqB;QACpD8F,aAAapC,cAAcV,iBAAiB,KAAK;IACnD,OAAO;QACL4C,YAAY;QACZC,iBAAiB5F;QACjB6F,aAAa;IACf;IAEA,IAAIL,kBAAkBnF,oBAAoB,IAAIsF,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQ5D,KAAK,CAACyD;QAChB;QACA,wEAAwE;QACxE,MAAM,IAAI5E,yBAAAA,qBAAqB;IACjC;IAEA,MAAMT,gBAAgBkF,kBAAkBlF,aAAa;IACrD,IAAIA,cAAciD,MAAM,EAAE;QACxB,IAAK,IAAIwC,IAAI,GAAGA,IAAIzF,cAAciD,MAAM,EAAEwC,IAAK;YAC7CD,QAAQ5D,KAAK,CAAC5B,aAAa,CAACyF,EAAE;QAChC;QAEA,MAAM,IAAIhF,yBAAAA,qBAAqB;IACjC;IAEA,IAAI,CAACyE,kBAAkBtF,mBAAmB,EAAE;QAC1C,IAAIsF,kBAAkBrF,kBAAkB,EAAE;YACxC,IAAIwF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIwE,kBAAkBpF,kBAAkB,EAAE;YAC/C,IAAIuF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3231, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["unstable_rethrow", "error", "isNextRouterError", "isBailoutToCSRError", "isDynamicServerError", "isDynamicPostpone", "isPostpone", "isHangingPromiseRejectionError", "Error", "cause"], "mappings": ";;;;+BAOg<PERSON>,oBAAAA;;;eAAAA;;;uCAP+B;4BACpB;8BACS;mCACF;kCACA;oCACG;AAE9B,SAASA,iBAAiBC,KAAc;IAC7C,IACEC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,UAClBE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,UACpBG,CAAAA,GAAAA,oBAAAA,oBAAoB,EAACH,UACrBI,CAAAA,GAAAA,kBAAAA,iBAAiB,EAACJ,UAClBK,CAAAA,GAAAA,YAAAA,UAAU,EAACL,UACXM,CAAAA,GAAAA,uBAAAA,8BAA8B,EAACN,QAC/B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBO,SAAS,WAAWP,OAAO;QAC9CD,iBAAiBC,MAAMQ,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACY<PERSON>,oBAAAA;;;eAAAA;;;AAAN,MAAMA,mBACX,OAAOC,WAAW,cAEZC,QAAQ,wHACRF,gBAAgB,GAEhBE,QAAQ,yHACRF,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3296, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "ReadonlyURLSearchParamsError", "Error", "constructor", "URLSearchParams", "append", "delete", "set", "sort"], "mappings": "AAAA,cAAc,GAAA;;;;;;;;;;;;;;;;;;;;;IAkCLA,uBAAuB,EAAA;eAAvBA;;IALAC,YAAY,EAAA;eAAZA,eAAAA,YAAY;;IAEZC,SAAS,EAAA;eAATA,WAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAFEC,iBAAiB,EAAA;eAAjBA,UAAAA,iBAAiB;;IAA3BC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAIRC,YAAY,EAAA;eAAZA,cAAAA,YAAY;;IACZC,gBAAgB,EAAA;eAAhBA,iBAAAA,gBAAgB;;;0BALmB;+BACf;0BACJ;2BACC;8BACG;iCACI;AAhCjC,MAAMC,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMV,gCAAgCW;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIJ;IACZ;IACA,wKAAwK,GACxKK,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,MAAM;QACJ,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,OAAO;QACL,MAAM,IAAIP;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3379, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].ServerInsertedHtml\n"], "names": ["module", "exports", "require", "vendored", "ServerInsertedHtml"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3386, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n"], "names": ["bailoutToClientRendering", "reason", "workStore", "workAsyncStorage", "getStore", "forceStatic", "isStaticGeneration", "BailoutToCSRError"], "mappings": ";;;;+BAGgBA,4BAAAA;;;eAAAA;;;8BAHkB;0CACD;AAE1B,SAASA,yBAAyBC,MAAc;IACrD,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAE3C,IAAIF,aAAAA,OAAAA,KAAAA,IAAAA,UAAWG,WAAW,EAAE;IAE5B,IAAIH,aAAAA,OAAAA,KAAAA,IAAAA,UAAWI,kBAAkB,EAAE,MAAM,OAAA,cAA6B,CAA7B,IAAIC,cAAAA,iBAAiB,CAACN,SAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAA4B;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3419, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/src/client/components/navigation.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "ServerInsertedHTMLContext", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "useParams", "usePathname", "useRouter", "useSearchParams", "useSelectedLayoutSegment", "useSelectedLayoutSegments", "useServerInsertedHTML", "useDynamicRouteParams", "window", "require", "undefined", "searchParams", "useContext", "SearchParamsContext", "readonlySearchParams", "useMemo", "bailoutToClientRendering", "PathnameContext", "router", "AppRouterContext", "Error", "PathParamsContext", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "getSegmentValue", "startsWith", "PAGE_SEGMENT_KEY", "push", "context", "LayoutRouterContext", "parentTree", "selectedLayoutSegments", "length", "selectedLayoutSegment", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0REA,uBAAuB,EAAA;eAAvBA,uBAAAA,uBAAuB;;IADvBC,YAAY,EAAA;eAAZA,uBAAAA,YAAY;;IApLZC,yBAAyB,EAAA;eAAzBA,iCAAAA,yBAAyB;;IAgLzBC,SAAS,EAAA;eAATA,uBAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,uBAAAA,QAAQ;;IAIRC,iBAAiB,EAAA;eAAjBA,uBAAAA,iBAAiB;;IADjBC,QAAQ,EAAA;eAARA,uBAAAA,QAAQ;;IADRC,YAAY,EAAA;eAAZA,uBAAAA,YAAY;;IAKZC,gBAAgB,EAAA;eAAhBA,uBAAAA,gBAAgB;;IApIFC,SAAS,EAAA;eAATA;;IA5DAC,WAAW,EAAA;eAAXA;;IAiCAC,SAAS,EAAA;eAATA;;IA9EAC,eAAe,EAAA;eAAfA;;IA6MAC,wBAAwB,EAAA;eAAxBA;;IA/BAC,yBAAyB,EAAA;eAAzBA;;IAtHdC,qBAAqB,EAAA;eAArBA,iCAAAA,qBAAqB;;;uBAnGa;+CAK7B;iDAKA;iCACyB;yBACsB;uCACd;iDAuFjC;AArFP,MAAMC,wBACJ,OAAOC,WAAW,cAEZC,QAAQ,kHACRF,qBAAqB,GACvBG;AAuBC,SAASP;IACd,MAAMQ,eAAeC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,iCAAAA,mBAAmB;IAEnD,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMC,uBAAuBC,CAAAA,GAAAA,OAAAA,OAAO,EAAC;QACnC,IAAI,CAACJ,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAIpB,uBAAAA,uBAAuB,CAACoB;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOH,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEQ,wBAAwB,EAAE,GAChCP,QAAQ;QACV,mEAAmE;QACnEO,yBAAyB;IAC3B;IAEA,OAAOF;AACT;AAoBO,SAASb;IACdM,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,8EAA8E;IAC9E,0EAA0E;IAC1E,OAAOK,CAAAA,GAAAA,OAAAA,UAAU,EAACK,iCAAAA,eAAe;AACnC;AA2BO,SAASf;IACd,MAAMgB,SAASN,CAAAA,GAAAA,OAAAA,UAAU,EAACO,+BAAAA,gBAAgB;IAC1C,IAAID,WAAW,MAAM;QACnB,MAAM,OAAA,cAAwD,CAAxD,IAAIE,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IAEA,OAAOF;AACT;AAoBO,SAASlB;IACdO,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,OAAOK,CAAAA,GAAAA,OAAAA,UAAU,EAACS,iCAAAA,iBAAiB;AACrC;AAEA,0EAA0E,GAC1E,wBAAwB;AACxB,SAASC,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,UAAAA,KAAAA,GAAAA,QAAQ;IACRC,IAAAA,gBAAAA,KAAAA,GAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,KAAA,OAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,IAAIM,eAAeC,CAAAA,GAAAA,iBAAAA,eAAe,EAACF;IAEnC,IAAI,CAACC,gBAAgBA,aAAaE,UAAU,CAACC,SAAAA,gBAAgB,GAAG;QAC9D,OAAOV;IACT;IAEAA,YAAYW,IAAI,CAACJ;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AA4BO,SAASrB,0BACdmB,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAM+B,UAAU1B,CAAAA,GAAAA,OAAAA,UAAU,EAAC2B,+BAAAA,mBAAmB;IAC9C,wFAAwF;IACxF,IAAI,CAACD,SAAS,OAAO;IAErB,OAAOhB,6BAA6BgB,QAAQE,UAAU,EAAEhB;AAC1D;AAqBO,SAASpB,yBACdoB,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAMkC,yBAAyBpC,0BAA0BmB;IAEzD,IAAI,CAACiB,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJnB,qBAAqB,aACjBiB,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,0BAA0BC,SAAAA,mBAAmB,GAChD,OACAD;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3604, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3611, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3647, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3674, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+MAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3714, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,+KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,+KAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3746, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3792, "column": 0}, "map": {"version": 3, "file": "save.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/icons/save.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('save', __iconNode);\n\nexport default Save;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}