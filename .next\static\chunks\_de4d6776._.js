(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/algorithms/planning.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PlanningAlgorithm": (()=>PlanningAlgorithm)
});
class PlanningAlgorithm {
    /**
   * 计算任务的综合分数
   */ calculateTaskScore(task) {
        // 基础分数：重要性权重0.6，紧急性权重0.4
        const baseScore = task.importance * 0.6 + task.urgency * 0.4;
        // 分类加权
        const categoryBonus = {
            work: 0,
            improvement: 2,
            entertainment: 1
        }[task.category];
        // 推迟惩罚：每推迟一次扣3分
        const postponePenalty = task.postponeCount * 3;
        // 截止时间紧迫性加权
        const deadlineBonus = this.calculateDeadlineUrgency(task.deadline);
        return baseScore + categoryBonus + postponePenalty + deadlineBonus;
    }
    /**
   * 根据截止时间计算紧迫性加权
   */ calculateDeadlineUrgency(deadline) {
        const now = new Date();
        const hoursUntilDeadline = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);
        if (hoursUntilDeadline < 0) return 10; // 已过期，最高优先级
        if (hoursUntilDeadline < 2) return 8; // 2小时内
        if (hoursUntilDeadline < 6) return 6; // 6小时内
        if (hoursUntilDeadline < 24) return 4; // 24小时内
        if (hoursUntilDeadline < 72) return 2; // 3天内
        return 0; // 3天以上
    }
    /**
   * 根据重要性和紧急性确定四象限
   */ classifyQuadrant(importance, urgency) {
        const isImportant = importance >= 4;
        const isUrgent = urgency >= 4;
        if (isImportant && isUrgent) return 1; // 重要且紧急
        if (isImportant && !isUrgent) return 2; // 重要不紧急
        if (!isImportant && isUrgent) return 3; // 不重要但紧急
        return 4; // 不重要不紧急
    }
    /**
   * 生成今日时间安排
   */ generateDailySchedule(tasks, userTimeConfig) {
        // 1. 过滤今日需要处理的任务
        const todayTasks = this.filterTodayTasks(tasks);
        // 2. 计算分数并分类
        const scoredTasks = todayTasks.map((task)=>({
                ...task,
                score: this.calculateTaskScore(task),
                quadrant: this.classifyQuadrant(task.importance, task.urgency)
            })).sort((a, b)=>{
            // 先按象限排序，再按分数排序
            if (a.quadrant !== b.quadrant) {
                return a.quadrant - b.quadrant;
            }
            return b.score - a.score;
        });
        // 3. 生成时间段（使用新的基于任务类型的算法）
        const timeSlots = this.generateTimeSlotsWithCategories(scoredTasks, userTimeConfig);
        // 4. 计算总时长
        const totalDuration = timeSlots.reduce((sum, slot)=>sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0);
        return {
            date: new Date(),
            timeSlots,
            totalTasks: todayTasks.length,
            estimatedDuration: totalDuration
        };
    }
    /**
   * 过滤今日需要处理的任务
   */ filterTodayTasks(tasks) {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tasks.filter((task)=>{
            // 包含今日截止的任务和未完成的高优先级任务
            const isToday = task.deadline <= tomorrow;
            const isHighPriority = task.importance >= 4 || task.urgency >= 4;
            const isPending = task.status === 'pending' || task.status === 'in-progress';
            return isPending && (isToday || isHighPriority);
        });
    }
    /**
   * 基于任务类型生成时间段安排
   */ generateTimeSlotsWithCategories(tasks, userTimeConfig) {
        const timeSlots = [];
        const today = new Date();
        // 使用默认配置如果没有提供用户配置
        const defaultConfig = {
            workStart: '09:00',
            workEnd: '18:00',
            categoryPreferences: {
                work: {
                    preferredTimes: [
                        '09:00-12:00',
                        '14:00-18:00'
                    ],
                    maxDaily: 480
                },
                improvement: {
                    preferredTimes: [
                        '07:00-09:00',
                        '19:00-21:00'
                    ],
                    maxDaily: 120
                },
                entertainment: {
                    preferredTimes: [
                        '20:00-22:00'
                    ],
                    maxDaily: 180
                }
            },
            fixedSlots: [
                {
                    start: '12:00',
                    end: '13:00',
                    type: 'meal',
                    label: '午餐时间'
                }
            ]
        };
        const config = userTimeConfig || defaultConfig;
        // 按任务类型分组
        const tasksByCategory = {
            work: tasks.filter((task)=>task.category === 'work'),
            improvement: tasks.filter((task)=>task.category === 'improvement'),
            entertainment: tasks.filter((task)=>task.category === 'entertainment')
        };
        // 为每种类型的任务安排时间
        for (const [category, categoryTasks] of Object.entries(tasksByCategory)){
            if (categoryTasks.length === 0) continue;
            const categoryPrefs = config.categoryPreferences[category];
            if (!categoryPrefs) continue;
            // 为该类型任务生成可用时间段
            const availableSlots = this.generateAvailableSlots(categoryPrefs.preferredTimes, config.fixedSlots, today);
            // 在可用时间段中安排任务
            this.scheduleTasksInSlots(categoryTasks, availableSlots, timeSlots);
        }
        return timeSlots.sort((a, b)=>a.startTime.getTime() - b.startTime.getTime());
    }
    /**
   * 生成时间段安排（保留原方法作为后备）
   */ generateTimeSlots(tasks, workHours) {
        const timeSlots = [];
        const today = new Date();
        // 解析工作时间
        const [startHour, startMinute] = workHours.start.split(':').map(Number);
        const [endHour, endMinute] = workHours.end.split(':').map(Number);
        let currentTime = new Date(today);
        currentTime.setHours(startHour, startMinute, 0, 0);
        const workEndTime = new Date(today);
        workEndTime.setHours(endHour, endMinute, 0, 0);
        for (const task of tasks){
            // 检查是否还有足够的工作时间
            const remainingWorkTime = workEndTime.getTime() - currentTime.getTime();
            const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒
            if (remainingWorkTime < taskDuration) {
                continue;
            }
            const endTime = new Date(currentTime.getTime() + taskDuration);
            timeSlots.push({
                task,
                startTime: new Date(currentTime),
                endTime,
                isFixed: false
            });
            // 更新当前时间，添加15分钟休息时间
            currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);
            // 如果超过工作时间，停止安排
            if (currentTime >= workEndTime) {
                break;
            }
        }
        return timeSlots;
    }
    /**
   * 获取四象限的描述
   */ getQuadrantDescription(quadrant) {
        const descriptions = {
            1: '重要且紧急 - 立即执行',
            2: '重要不紧急 - 计划执行',
            3: '不重要但紧急 - 委托处理',
            4: '不重要不紧急 - 减少或删除'
        };
        return descriptions[quadrant];
    }
    /**
   * 生成可用时间段
   */ generateAvailableSlots(preferredTimes, fixedSlots, date) {
        const availableSlots = [];
        for (const timeRange of preferredTimes){
            const [startTime, endTime] = timeRange.split('-');
            const [startHour, startMinute] = startTime.split(':').map(Number);
            const [endHour, endMinute] = endTime.split(':').map(Number);
            const slotStart = new Date(date);
            slotStart.setHours(startHour, startMinute, 0, 0);
            const slotEnd = new Date(date);
            slotEnd.setHours(endHour, endMinute, 0, 0);
            // 检查是否与固定时间段冲突
            let hasConflict = false;
            for (const fixedSlot of fixedSlots){
                const [fixedStartHour, fixedStartMinute] = fixedSlot.start.split(':').map(Number);
                const [fixedEndHour, fixedEndMinute] = fixedSlot.end.split(':').map(Number);
                const fixedStart = new Date(date);
                fixedStart.setHours(fixedStartHour, fixedStartMinute, 0, 0);
                const fixedEnd = new Date(date);
                fixedEnd.setHours(fixedEndHour, fixedEndMinute, 0, 0);
                // 检查时间段重叠
                if (slotStart < fixedEnd && slotEnd > fixedStart) {
                    hasConflict = true;
                    break;
                }
            }
            if (!hasConflict) {
                availableSlots.push({
                    start: slotStart,
                    end: slotEnd
                });
            }
        }
        return availableSlots;
    }
    /**
   * 在可用时间段中安排任务
   */ scheduleTasksInSlots(tasks, availableSlots, timeSlots) {
        let currentSlotIndex = 0;
        let currentTime = availableSlots[0]?.start;
        if (!currentTime) return;
        for (const task of tasks){
            const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒
            // 寻找合适的时间段
            while(currentSlotIndex < availableSlots.length){
                const currentSlot = availableSlots[currentSlotIndex];
                const remainingTime = currentSlot.end.getTime() - currentTime.getTime();
                if (remainingTime >= taskDuration) {
                    // 在当前时间段安排任务
                    const endTime = new Date(currentTime.getTime() + taskDuration);
                    timeSlots.push({
                        task,
                        startTime: new Date(currentTime),
                        endTime,
                        isFixed: false
                    });
                    // 更新当前时间，添加15分钟休息时间
                    currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);
                    break;
                } else {
                    // 移动到下一个时间段
                    currentSlotIndex++;
                    currentTime = availableSlots[currentSlotIndex]?.start;
                    if (!currentTime) break;
                }
            }
        }
    }
    /**
   * 获取任务建议
   */ getTaskRecommendation(task) {
        if (task.quadrant === 1) {
            return '🔥 高优先级任务，建议立即处理';
        } else if (task.quadrant === 2) {
            return '📅 重要任务，建议合理安排时间';
        } else if (task.quadrant === 3) {
            return '⚡ 紧急但不重要，考虑委托或快速处理';
        } else {
            return '🤔 优先级较低，可以延后或删除';
        }
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/algorithms/balance.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BalanceAlgorithm": (()=>BalanceAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
class BalanceAlgorithm {
    // 理想的时间分配比例
    IDEAL_RATIOS = {
        work: 0.6,
        improvement: 0.25,
        entertainment: 0.15
    };
    /**
   * 分析用户的生活平衡状况
   */ async analyzeWeeklyBalance(userId) {
        try {
            // 获取最近7天的统计数据
            const stats = await this.getDailyStats(userId, 7);
            if (stats.length === 0) {
                return {
                    workRatio: 0,
                    improvementRatio: 0,
                    entertainmentRatio: 0,
                    balanceScore: 0,
                    recommendation: "开始记录你的任务来获得生活平衡分析 📊"
                };
            }
            // 计算总时间和各分类时间
            const totalTime = stats.reduce((sum, day)=>sum + day.workTime + day.improvementTime + day.entertainmentTime, 0);
            if (totalTime === 0) {
                return {
                    workRatio: 0,
                    improvementRatio: 0,
                    entertainmentRatio: 0,
                    balanceScore: 0,
                    recommendation: "还没有完成任务记录，开始你的第一个任务吧！ 🚀"
                };
            }
            // 计算各分类的时间比例
            const ratios = {
                work: stats.reduce((sum, day)=>sum + day.workTime, 0) / totalTime,
                improvement: stats.reduce((sum, day)=>sum + day.improvementTime, 0) / totalTime,
                entertainment: stats.reduce((sum, day)=>sum + day.entertainmentTime, 0) / totalTime
            };
            // 计算平衡分数
            const balanceScore = this.calculateBalanceScore(ratios);
            // 生成建议
            const recommendation = this.generateRecommendation(ratios, stats);
            return {
                workRatio: ratios.work,
                improvementRatio: ratios.improvement,
                entertainmentRatio: ratios.entertainment,
                balanceScore,
                recommendation
            };
        } catch (error) {
            console.error('Error analyzing balance:', error);
            throw new Error('Failed to analyze balance');
        }
    }
    /**
   * 计算生活平衡分数 (0-100)
   */ calculateBalanceScore(ratios) {
        let score = 100;
        // 计算每个分类与理想比例的偏差
        Object.keys(this.IDEAL_RATIOS).forEach((category)=>{
            const ideal = this.IDEAL_RATIOS[category];
            const actual = ratios[category];
            const deviation = Math.abs(ideal - actual);
            // 偏差越大，扣分越多
            score -= deviation * 100;
        });
        return Math.max(0, Math.round(score));
    }
    /**
   * 生成个性化建议
   */ generateRecommendation(ratios, stats) {
        const recommendations = [];
        // 分析工作时间
        if (ratios.work > 0.8) {
            recommendations.push("⚠️ 工作时间过长，建议增加休息和娱乐时间");
        } else if (ratios.work < 0.3) {
            recommendations.push("💼 工作时间较少，可以适当增加工作或学习时间");
        }
        // 分析提升时间
        if (ratios.improvement < 0.1) {
            recommendations.push("📚 建议安排一些学习或自我提升的活动");
        } else if (ratios.improvement > 0.4) {
            recommendations.push("🎯 学习时间充足，注意劳逸结合");
        }
        // 分析娱乐时间
        if (ratios.entertainment < 0.05) {
            recommendations.push("🎮 需要更多的放松和娱乐时间");
        } else if (ratios.entertainment > 0.3) {
            recommendations.push("⏰ 娱乐时间较多，可以适当增加工作或学习");
        }
        // 分析连续性
        const recentDays = stats.slice(-3); // 最近3天
        const hasConsistentWork = recentDays.every((day)=>day.workTime > 0);
        const hasConsistentImprovement = recentDays.every((day)=>day.improvementTime > 0);
        if (!hasConsistentWork) {
            recommendations.push("🔄 建议保持每日工作的连续性");
        }
        if (!hasConsistentImprovement) {
            recommendations.push("📈 建议每天安排一些自我提升时间");
        }
        // 如果没有特别的建议，给出正面反馈
        if (recommendations.length === 0) {
            const score = this.calculateBalanceScore(ratios);
            if (score >= 80) {
                return "✨ 生活平衡状态优秀，继续保持！";
            } else if (score >= 60) {
                return "👍 生活平衡状态良好，可以微调优化";
            } else {
                return "🎯 生活平衡有改善空间，建议关注时间分配";
            }
        }
        return recommendations.join(" • ");
    }
    /**
   * 获取用户的每日统计数据
   */ async getDailyStats(userId, days) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('daily_stats').select('*').eq('user_id', userId).gte('date', startDate.toISOString().split('T')[0]).order('date', {
            ascending: true
        });
        if (error) {
            console.error('Error fetching daily stats:', error);
            return [];
        }
        return data.map((stat)=>({
                id: stat.id,
                userId: stat.user_id,
                date: new Date(stat.date),
                workTime: stat.work_time,
                improvementTime: stat.improvement_time,
                entertainmentTime: stat.entertainment_time,
                tasksCompleted: stat.tasks_completed,
                tasksPostponed: stat.tasks_postponed,
                balanceScore: stat.balance_score || 0
            }));
    }
    /**
   * 更新今日统计数据
   */ async updateTodayStats(userId, category, timeSpent) {
        const today = new Date().toISOString().split('T')[0];
        try {
            // 获取今日统计
            const { data: existing } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('daily_stats').select('*').eq('user_id', userId).eq('date', today).single();
            const updateData = {};
            if (category === 'work') {
                updateData.work_time = (existing?.work_time || 0) + timeSpent;
            } else if (category === 'improvement') {
                updateData.improvement_time = (existing?.improvement_time || 0) + timeSpent;
            } else if (category === 'entertainment') {
                updateData.entertainment_time = (existing?.entertainment_time || 0) + timeSpent;
            }
            if (existing) {
                // 更新现有记录
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('daily_stats').update(updateData).eq('id', existing.id);
            } else {
                // 创建新记录
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('daily_stats').insert({
                    user_id: userId,
                    date: today,
                    ...updateData
                });
            }
        } catch (error) {
            console.error('Error updating daily stats:', error);
        }
    }
    /**
   * 获取平衡状态的颜色指示
   */ getBalanceStatusColor(score) {
        if (score >= 80) return 'text-green-600';
        if (score >= 60) return 'text-yellow-600';
        if (score >= 40) return 'text-orange-600';
        return 'text-red-600';
    }
    /**
   * 获取平衡状态的描述
   */ getBalanceStatusText(score) {
        if (score >= 80) return '优秀';
        if (score >= 60) return '良好';
        if (score >= 40) return '一般';
        return '需要改善';
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/algorithms/fix.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "FixAlgorithm": (()=>FixAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
class FixAlgorithm {
    // 推迟次数阈值
    POSTPONE_THRESHOLDS = {
        low: 1,
        medium: 3,
        high: 5,
        critical: 8
    };
    /**
   * 分析需要修复的推迟任务
   */ async analyzePostponedTasks(userId) {
        try {
            // 获取所有推迟的任务
            const postponedTasks = await this.getPostponedTasks(userId);
            return postponedTasks.map((task)=>{
                const urgencyLevel = this.calculateUrgencyLevel(task);
                const suggestion = this.generateFixSuggestion(task);
                const daysSinceCreated = this.calculateDaysSince(task.createdAt);
                return {
                    task,
                    postponeCount: task.postponeCount,
                    daysSinceCreated,
                    urgencyLevel,
                    suggestion,
                    shouldAlert: urgencyLevel !== 'low'
                };
            }).filter((alert)=>alert.shouldAlert); // 只返回需要提醒的任务
        } catch (error) {
            console.error('Error analyzing postponed tasks:', error);
            return [];
        }
    }
    /**
   * 计算任务的紧急程度
   */ calculateUrgencyLevel(task) {
        const { postponeCount, deadline } = task;
        const daysSinceDeadline = this.calculateDaysSince(deadline);
        const daysSinceCreated = this.calculateDaysSince(task.createdAt);
        // 综合考虑推迟次数、截止时间和创建时间
        let urgencyScore = 0;
        // 推迟次数评分
        if (postponeCount >= this.POSTPONE_THRESHOLDS.critical) urgencyScore += 4;
        else if (postponeCount >= this.POSTPONE_THRESHOLDS.high) urgencyScore += 3;
        else if (postponeCount >= this.POSTPONE_THRESHOLDS.medium) urgencyScore += 2;
        else if (postponeCount >= this.POSTPONE_THRESHOLDS.low) urgencyScore += 1;
        // 截止时间评分
        if (daysSinceDeadline > 0) urgencyScore += 3; // 已过期
        else if (daysSinceDeadline > -1) urgencyScore += 2; // 1天内到期
        else if (daysSinceDeadline > -3) urgencyScore += 1; // 3天内到期
        // 创建时间评分（任务存在时间过长）
        if (daysSinceCreated > 14) urgencyScore += 2; // 超过2周
        else if (daysSinceCreated > 7) urgencyScore += 1; // 超过1周
        // 重要性和紧急性加权
        if (task.importance >= 4) urgencyScore += 1;
        if (task.urgency >= 4) urgencyScore += 1;
        // 根据总分确定紧急程度
        if (urgencyScore >= 7) return 'critical';
        if (urgencyScore >= 5) return 'high';
        if (urgencyScore >= 3) return 'medium';
        return 'low';
    }
    /**
   * 生成修复建议
   */ generateFixSuggestion(task) {
        const urgencyLevel = this.calculateUrgencyLevel(task);
        const { postponeCount, category, estimatedDuration } = task;
        // 基于紧急程度的基础建议
        const baseSuggestions = {
            critical: "🚨 紧急处理：这个任务已经严重延期，建议立即处理或重新评估其必要性",
            high: "⚠️ 重点关注：建议将任务分解为更小的部分，或调整截止时间",
            medium: "💡 优化建议：可以设置更具体的时间安排或降低任务难度",
            low: "📝 轻微提醒：建议适当调整任务优先级或时间安排"
        };
        let suggestion = baseSuggestions[urgencyLevel];
        // 基于推迟次数的具体建议
        if (postponeCount >= 5) {
            suggestion += "\n• 考虑将任务分解为5-10分钟的小任务";
        } else if (postponeCount >= 3) {
            suggestion += "\n• 尝试番茄工作法，专注25分钟";
        }
        // 基于任务时长的建议
        if (estimatedDuration > 120) {
            suggestion += "\n• 任务时间较长，建议分解为多个子任务";
        }
        // 基于分类的建议
        if (category === 'work') {
            suggestion += "\n• 工作任务：考虑在精力最好的时间段处理";
        } else if (category === 'improvement') {
            suggestion += "\n• 提升任务：可以设置学习奖励机制";
        } else if (category === 'entertainment') {
            suggestion += "\n• 娱乐任务：确保这确实是你想要的放松方式";
        }
        return suggestion;
    }
    /**
   * 获取推迟的任务
   */ async getPostponedTasks(userId) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*').eq('user_id', userId).gt('postpone_count', 0).in('status', [
            'pending',
            'postponed'
        ]).order('postpone_count', {
            ascending: false
        });
        if (error) {
            console.error('Error fetching postponed tasks:', error);
            return [];
        }
        return data.map((task)=>({
                id: task.id,
                userId: task.user_id,
                title: task.title,
                description: task.description,
                category: task.category,
                importance: task.importance,
                urgency: task.urgency,
                deadline: new Date(task.deadline),
                estimatedDuration: task.estimated_duration,
                status: task.status,
                postponeCount: task.postpone_count,
                createdAt: new Date(task.created_at),
                updatedAt: new Date(task.updated_at)
            }));
    }
    /**
   * 计算距离某个日期的天数
   */ calculateDaysSince(date) {
        const now = new Date();
        const diffTime = now.getTime() - date.getTime();
        return Math.floor(diffTime / (1000 * 60 * 60 * 24));
    }
    /**
   * 推迟任务
   */ async postponeTask(taskId, reason) {
        try {
            // 获取当前任务
            const { data: task, error: fetchError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('postpone_count').eq('id', taskId).single();
            if (fetchError) throw fetchError;
            // 更新推迟次数
            const { error: updateError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').update({
                postpone_count: (task.postpone_count || 0) + 1,
                status: 'postponed',
                updated_at: new Date().toISOString()
            }).eq('id', taskId);
            if (updateError) throw updateError;
        // 记录推迟历史（如果有历史表的话）
        // 这里可以扩展记录推迟原因等信息
        } catch (error) {
            console.error('Error postponing task:', error);
            throw new Error('Failed to postpone task');
        }
    }
    /**
   * 重置任务的推迟状态
   */ async resetTaskPostponeStatus(taskId) {
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').update({
                postpone_count: 0,
                status: 'pending',
                updated_at: new Date().toISOString()
            }).eq('id', taskId);
            if (error) throw error;
        } catch (error) {
            console.error('Error resetting task postpone status:', error);
            throw new Error('Failed to reset task postpone status');
        }
    }
    /**
   * 获取推迟统计信息
   */ async getPostponeStats(userId) {
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('postpone_count, category').eq('user_id', userId).gt('postpone_count', 0);
            if (error) throw error;
            if (!data || data.length === 0) {
                return {
                    totalPostponedTasks: 0,
                    averagePostponeCount: 0,
                    mostPostponedCategory: 'work'
                };
            }
            const totalPostponedTasks = data.length;
            const averagePostponeCount = data.reduce((sum, task)=>sum + task.postpone_count, 0) / totalPostponedTasks;
            // 统计各分类的推迟次数
            const categoryStats = data.reduce((acc, task)=>{
                acc[task.category] = (acc[task.category] || 0) + task.postpone_count;
                return acc;
            }, {});
            const mostPostponedCategory = Object.keys(categoryStats).reduce((a, b)=>categoryStats[a] > categoryStats[b] ? a : b);
            return {
                totalPostponedTasks,
                averagePostponeCount: Math.round(averagePostponeCount * 10) / 10,
                mostPostponedCategory
            };
        } catch (error) {
            console.error('Error getting postpone stats:', error);
            return {
                totalPostponedTasks: 0,
                averagePostponeCount: 0,
                mostPostponedCategory: 'work'
            };
        }
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/store/useTaskStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTaskStore": (()=>useTaskStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$algorithms$2f$planning$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/algorithms/planning.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$algorithms$2f$balance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/algorithms/balance.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$algorithms$2f$fix$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/algorithms/fix.ts [app-client] (ecmascript)");
;
;
;
;
;
const useTaskStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        // Initial state
        tasks: [],
        dailySchedule: null,
        balanceAnalysis: null,
        postponedAlerts: [],
        loading: false,
        error: null,
        // Algorithm instances
        planningAlgorithm: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$algorithms$2f$planning$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlanningAlgorithm"](),
        balanceAlgorithm: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$algorithms$2f$balance$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BalanceAlgorithm"](),
        fixAlgorithm: new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$algorithms$2f$fix$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FixAlgorithm"](),
        // Fetch tasks
        fetchTasks: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*').eq('user_id', userId).order('created_at', {
                    ascending: false
                });
                if (error) throw error;
                const tasks = data.map((task)=>({
                        id: task.id,
                        userId: task.user_id,
                        title: task.title,
                        description: task.description,
                        category: task.category,
                        importance: task.importance,
                        urgency: task.urgency,
                        deadline: new Date(task.deadline),
                        estimatedDuration: task.estimated_duration,
                        status: task.status,
                        postponeCount: task.postpone_count,
                        createdAt: new Date(task.created_at),
                        updatedAt: new Date(task.updated_at)
                    }));
                set({
                    tasks,
                    loading: false
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Create task
        createTask: async (taskData)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').insert({
                    user_id: taskData.userId,
                    title: taskData.title,
                    description: taskData.description,
                    category: taskData.category,
                    importance: taskData.importance,
                    urgency: taskData.urgency,
                    deadline: taskData.deadline.toISOString(),
                    estimated_duration: taskData.estimatedDuration,
                    status: taskData.status,
                    postpone_count: taskData.postponeCount
                }).select().single();
                if (error) throw error;
                const newTask = {
                    id: data.id,
                    userId: data.user_id,
                    title: data.title,
                    description: data.description,
                    category: data.category,
                    importance: data.importance,
                    urgency: data.urgency,
                    deadline: new Date(data.deadline),
                    estimatedDuration: data.estimated_duration,
                    status: data.status,
                    postponeCount: data.postpone_count,
                    createdAt: new Date(data.created_at),
                    updatedAt: new Date(data.updated_at)
                };
                set((state)=>({
                        tasks: [
                            newTask,
                            ...state.tasks
                        ],
                        loading: false
                    }));
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to create task';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        // Update task
        updateTask: async (id, updates)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const updateData = {};
                if (updates.title) updateData.title = updates.title;
                if (updates.description !== undefined) updateData.description = updates.description;
                if (updates.category) updateData.category = updates.category;
                if (updates.importance) updateData.importance = updates.importance;
                if (updates.urgency) updateData.urgency = updates.urgency;
                if (updates.deadline) updateData.deadline = updates.deadline.toISOString();
                if (updates.estimatedDuration) updateData.estimated_duration = updates.estimatedDuration;
                if (updates.status) updateData.status = updates.status;
                if (updates.postponeCount !== undefined) updateData.postpone_count = updates.postponeCount;
                updateData.updated_at = new Date().toISOString();
                const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').update(updateData).eq('id', id);
                if (error) throw error;
                set((state)=>({
                        tasks: state.tasks.map((task)=>task.id === id ? {
                                ...task,
                                ...updates,
                                updatedAt: new Date()
                            } : task),
                        loading: false
                    }));
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to update task';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        // Delete task
        deleteTask: async (id)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').delete().eq('id', id);
                if (error) throw error;
                set((state)=>({
                        tasks: state.tasks.filter((task)=>task.id !== id),
                        loading: false
                    }));
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete task';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        // Complete task
        completeTask: async (id, actualDuration, satisfaction)=>{
            try {
                const { updateTask, balanceAlgorithm } = get();
                const task = get().tasks.find((t)=>t.id === id);
                if (!task) throw new Error('Task not found');
                // Update task status
                await updateTask(id, {
                    status: 'completed'
                });
                // Record completion
                if (actualDuration && satisfaction) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('task_completions').insert({
                        task_id: id,
                        actual_duration: actualDuration,
                        satisfaction_score: satisfaction
                    });
                }
                // Update daily stats
                await balanceAlgorithm.updateTodayStats(task.userId, task.category, actualDuration || task.estimatedDuration);
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';
                set({
                    error: errorMessage
                });
                throw error;
            }
        },
        // Postpone task
        postponeTask: async (id, reason)=>{
            try {
                const { fixAlgorithm } = get();
                await fixAlgorithm.postponeTask(id, reason);
                // Refresh tasks
                const task = get().tasks.find((t)=>t.id === id);
                if (task) {
                    await get().fetchTasks(task.userId);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to postpone task';
                set({
                    error: errorMessage
                });
                throw error;
            }
        },
        // Generate daily schedule
        generateDailySchedule: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { tasks, planningAlgorithm } = get();
                const userTasks = tasks.filter((task)=>task.userId === userId);
                // 获取用户时间配置
                let userTimeConfig = null;
                try {
                    const { data: userProfile } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('user_profiles').select('time_config').eq('id', userId).single();
                    userTimeConfig = userProfile?.time_config;
                } catch (error) {
                    console.log('No user time config found, using defaults');
                }
                const schedule = planningAlgorithm.generateDailySchedule(userTasks, userTimeConfig);
                set({
                    dailySchedule: schedule,
                    loading: false
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to generate schedule';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Analyze balance
        analyzeBalance: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { balanceAlgorithm } = get();
                const analysis = await balanceAlgorithm.analyzeWeeklyBalance(userId);
                set({
                    balanceAnalysis: analysis,
                    loading: false
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to analyze balance';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Analyze postponed tasks
        analyzePostponedTasks: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { fixAlgorithm } = get();
                const alerts = await fixAlgorithm.analyzePostponedTasks(userId);
                set({
                    postponedAlerts: alerts,
                    loading: false
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to analyze postponed tasks';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Utility actions
        setLoading: (loading)=>set({
                loading
            }),
        setError: (error)=>set({
                error
            }),
        clearError: ()=>set({
                error: null
            })
    }));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/QuadrantSelector.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>QuadrantSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function QuadrantSelector({ importance, urgency, onChange }) {
    _s();
    const [selectedQuadrant, setSelectedQuadrant] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // 根据重要性和紧急性确定象限
    const getQuadrant = (imp, urg)=>{
        if (imp >= 4 && urg >= 4) return 1; // 重要且紧急
        if (imp >= 4 && urg < 4) return 2; // 重要不紧急
        if (imp < 4 && urg >= 4) return 3; // 不重要但紧急
        return 4; // 不重要不紧急
    };
    const currentQuadrant = getQuadrant(importance, urgency);
    // 象限配置
    const quadrants = [
        {
            id: 1,
            title: '重要且紧急',
            description: '立即处理',
            color: 'bg-red-100 border-red-300 hover:bg-red-200',
            selectedColor: 'bg-red-200 border-red-500',
            textColor: 'text-red-800',
            importance: 5,
            urgency: 5,
            position: 'top-right'
        },
        {
            id: 2,
            title: '重要不紧急',
            description: '计划安排',
            color: 'bg-blue-100 border-blue-300 hover:bg-blue-200',
            selectedColor: 'bg-blue-200 border-blue-500',
            textColor: 'text-blue-800',
            importance: 5,
            urgency: 2,
            position: 'top-left'
        },
        {
            id: 3,
            title: '不重要但紧急',
            description: '委托处理',
            color: 'bg-yellow-100 border-yellow-300 hover:bg-yellow-200',
            selectedColor: 'bg-yellow-200 border-yellow-500',
            textColor: 'text-yellow-800',
            importance: 2,
            urgency: 5,
            position: 'bottom-right'
        },
        {
            id: 4,
            title: '不重要不紧急',
            description: '减少或删除',
            color: 'bg-gray-100 border-gray-300 hover:bg-gray-200',
            selectedColor: 'bg-gray-200 border-gray-500',
            textColor: 'text-gray-800',
            importance: 2,
            urgency: 2,
            position: 'bottom-left'
        }
    ];
    const handleQuadrantClick = (quadrant)=>{
        setSelectedQuadrant(quadrant.id);
        onChange(quadrant.importance, quadrant.urgency);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-sm font-medium text-gray-700 mb-2",
                children: "任务优先级（点击象限选择）"
            }, void 0, false, {
                fileName: "[project]/src/components/QuadrantSelector.tsx",
                lineNumber: 79,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative w-full max-w-md mx-auto",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 font-medium",
                        children: "紧急程度 →"
                    }, void 0, false, {
                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                        lineNumber: 86,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute -left-12 top-1/2 transform -translate-y-1/2 -rotate-90 text-xs text-gray-500 font-medium",
                        children: "重要程度 →"
                    }, void 0, false, {
                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                        lineNumber: 89,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-2 gap-2 w-80 h-80",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 2 ? quadrants[1].selectedColor : quadrants[1].color}
              ${quadrants[1].textColor}
            `,
                                onClick: ()=>handleQuadrantClick(quadrants[1]),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-1",
                                        children: "重要"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 105,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-2",
                                        children: "不紧急"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 106,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs opacity-75",
                                        children: "计划安排"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 107,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs mt-1 font-bold",
                                        children: "象限 II"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 108,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 96,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 1 ? quadrants[0].selectedColor : quadrants[0].color}
              ${quadrants[0].textColor}
            `,
                                onClick: ()=>handleQuadrantClick(quadrants[0]),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-1",
                                        children: "重要"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 121,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-2",
                                        children: "紧急"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 122,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs opacity-75",
                                        children: "立即处理"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 123,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs mt-1 font-bold",
                                        children: "象限 I"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 124,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 112,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 4 ? quadrants[3].selectedColor : quadrants[3].color}
              ${quadrants[3].textColor}
            `,
                                onClick: ()=>handleQuadrantClick(quadrants[3]),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-1",
                                        children: "不重要"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 137,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-2",
                                        children: "不紧急"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 138,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs opacity-75",
                                        children: "减少删除"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 139,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs mt-1 font-bold",
                                        children: "象限 IV"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 140,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 128,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `
              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200
              flex flex-col justify-center items-center text-center
              ${currentQuadrant === 3 ? quadrants[2].selectedColor : quadrants[2].color}
              ${quadrants[2].textColor}
            `,
                                onClick: ()=>handleQuadrantClick(quadrants[2]),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-1",
                                        children: "不重要"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 153,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "font-semibold text-sm mb-2",
                                        children: "紧急"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 154,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs opacity-75",
                                        children: "委托处理"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 155,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-xs mt-1 font-bold",
                                        children: "象限 III"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 156,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 144,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                        lineNumber: 94,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-4 text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-600",
                                children: [
                                    "当前选择：",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            "象限 ",
                                            currentQuadrant
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                                        lineNumber: 163,
                                        columnNumber: 18
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 162,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-500 mt-1",
                                children: [
                                    "重要性: ",
                                    importance,
                                    "/5 | 紧急性: ",
                                    urgency,
                                    "/5"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/QuadrantSelector.tsx",
                                lineNumber: 165,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/QuadrantSelector.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/QuadrantSelector.tsx",
                lineNumber: 84,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/QuadrantSelector.tsx",
        lineNumber: 78,
        columnNumber: 5
    }, this);
}
_s(QuadrantSelector, "wM0tDpHF0NZPfWBiiwo7MJWeMmk=");
_c = QuadrantSelector;
var _c;
__turbopack_context__.k.register(_c, "QuadrantSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/tasks/new/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>NewTask)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useAuthStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useTaskStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useTaskStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-client] (ecmascript) <export default as Save>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$QuadrantSelector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/QuadrantSelector.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function NewTask() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])();
    const { createTask, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useTaskStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTaskStore"])();
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        title: '',
        category: 'work',
        importance: 3,
        urgency: 3,
        deadline: '',
        estimatedDuration: 1 // 改为小时单位
    });
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!user) {
            router.push('/auth/signin');
            return;
        }
        try {
            await createTask({
                userId: user.id,
                title: formData.title,
                description: '',
                category: formData.category,
                importance: formData.importance,
                urgency: formData.urgency,
                deadline: new Date(formData.deadline),
                estimatedDuration: formData.estimatedDuration * 60,
                status: 'pending',
                postponeCount: 0
            });
            router.push('/dashboard');
        } catch (error) {
            console.error('Failed to create task:', error);
        }
    };
    const handleChange = (e)=>{
        const { name, value, type } = e.target;
        setFormData((prev)=>({
                ...prev,
                [name]: type === 'number' ? parseInt(value) : value
            }));
    };
    // 处理四象限选择器的变化
    const handleQuadrantChange = (importance, urgency)=>{
        setFormData((prev)=>({
                ...prev,
                importance,
                urgency
            }));
    };
    // 设置默认截止时间为明天
    const getDefaultDeadline = ()=>{
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(18, 0, 0, 0); // 默认下午6点
        return tomorrow.toISOString().slice(0, 16); // YYYY-MM-DDTHH:MM format
    };
    if (!formData.deadline) {
        setFormData((prev)=>({
                ...prev,
                deadline: getDefaultDeadline()
            }));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "bg-white shadow-sm border-b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-3xl mx-auto px-4 sm:px-6 lg:px-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center h-16",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>router.back(),
                                className: "flex items-center text-gray-600 hover:text-gray-900 mr-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                        className: "h-5 w-5 mr-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 91,
                                        columnNumber: 15
                                    }, this),
                                    "返回"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                lineNumber: 87,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-xl font-semibold text-gray-900",
                                children: "创建新任务"
                            }, void 0, false, {
                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                lineNumber: 94,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/tasks/new/page.tsx",
                        lineNumber: 86,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/tasks/new/page.tsx",
                    lineNumber: 85,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/tasks/new/page.tsx",
                lineNumber: 84,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg shadow p-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                        onSubmit: handleSubmit,
                        className: "space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        htmlFor: "title",
                                        className: "block text-sm font-medium text-gray-700 mb-2",
                                        children: "任务标题 *"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 105,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        type: "text",
                                        id: "title",
                                        name: "title",
                                        required: true,
                                        value: formData.title,
                                        onChange: handleChange,
                                        className: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",
                                        placeholder: "请输入任务标题"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 108,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                lineNumber: 104,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$QuadrantSelector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    importance: formData.importance,
                                    urgency: formData.urgency,
                                    onChange: handleQuadrantChange
                                }, void 0, false, {
                                    fileName: "[project]/src/app/tasks/new/page.tsx",
                                    lineNumber: 122,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                lineNumber: 121,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        htmlFor: "category",
                                        className: "block text-sm font-medium text-gray-700 mb-2",
                                        children: "任务分类 *"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 131,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                        id: "category",
                                        name: "category",
                                        required: true,
                                        value: formData.category,
                                        onChange: handleChange,
                                        className: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "work",
                                                children: "工作"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 142,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "improvement",
                                                children: "提升"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 143,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: "entertainment",
                                                children: "娱乐"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 144,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 134,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-sm text-gray-500",
                                        children: "工作：日常工作任务 | 提升：学习和自我提升 | 娱乐：休闲和放松"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 146,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                lineNumber: 130,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "deadline",
                                                className: "block text-sm font-medium text-gray-700 mb-2",
                                                children: "截止时间 *"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 156,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "datetime-local",
                                                id: "deadline",
                                                name: "deadline",
                                                required: true,
                                                value: formData.deadline,
                                                onChange: handleChange,
                                                className: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 159,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 155,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "estimatedDuration",
                                                className: "block text-sm font-medium text-gray-700 mb-2",
                                                children: "预估时长（小时）*"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 171,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "number",
                                                id: "estimatedDuration",
                                                name: "estimatedDuration",
                                                required: true,
                                                min: "0.25",
                                                max: "8",
                                                step: "0.25",
                                                value: formData.estimatedDuration,
                                                onChange: handleChange,
                                                className: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 174,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "mt-1 text-sm text-gray-500",
                                                children: "建议：0.25-8小时（15分钟-8小时）"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 186,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 170,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-end space-x-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>router.back(),
                                        className: "px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 194,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "submit",
                                        disabled: loading,
                                        className: "flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",
                                        children: [
                                            loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 207,
                                                columnNumber: 19
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__["Save"], {
                                                className: "h-4 w-4 mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                                lineNumber: 209,
                                                columnNumber: 19
                                            }, this),
                                            "创建任务"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/tasks/new/page.tsx",
                                        lineNumber: 201,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/tasks/new/page.tsx",
                                lineNumber: 193,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/tasks/new/page.tsx",
                        lineNumber: 102,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/tasks/new/page.tsx",
                    lineNumber: 101,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/tasks/new/page.tsx",
                lineNumber: 100,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/tasks/new/page.tsx",
        lineNumber: 82,
        columnNumber: 5
    }, this);
}
_s(NewTask, "AlqigHMMVink7eWMYn/tqWq4Wyo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useTaskStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTaskStore"]
    ];
});
_c = NewTask;
var _c;
__turbopack_context__.k.register(_c, "NewTask");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/navigation.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/navigation.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "hasA11yProp": (()=>hasA11yProp),
    "mergeClasses": (()=>mergeClasses),
    "toCamelCase": (()=>toCamelCase),
    "toKebabCase": (()=>toKebabCase),
    "toPascalCase": (()=>toPascalCase)
});
const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
const toCamelCase = (string)=>string.replace(/^([A-Z])|[\s-_]+(\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());
const toPascalCase = (string)=>{
    const camelCase = toCamelCase(string);
    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
};
const mergeClasses = (...classes)=>classes.filter((className, index, array)=>{
        return Boolean(className) && className.trim() !== "" && array.indexOf(className) === index;
    }).join(" ").trim();
const hasA11yProp = (props)=>{
    for(const prop in props){
        if (prop.startsWith("aria-") || prop === "role" || prop === "title") {
            return true;
        }
    }
};
;
 //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>defaultAttributes)
});
var defaultAttributes = {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: 2,
    strokeLinecap: "round",
    strokeLinejoin: "round"
};
;
 //# sourceMappingURL=defaultAttributes.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/Icon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>Icon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)");
;
;
;
const Icon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ color = "currentColor", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = "", children, iconNode, ...rest }, ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        ref,
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        width: size,
        height: size,
        stroke: color,
        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeClasses"])("lucide", className),
        ...!children && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasA11yProp"])(rest) && {
            "aria-hidden": "true"
        },
        ...rest
    }, [
        ...iconNode.map(([tag, attrs])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(tag, attrs)),
        ...Array.isArray(children) ? children : [
            children
        ]
    ]));
;
 //# sourceMappingURL=Icon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>createLucideIcon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/Icon.js [app-client] (ecmascript)");
;
;
;
const createLucideIcon = (iconName, iconNode)=>{
    const Component = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            ref,
            iconNode,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeClasses"])(`lucide-${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toKebabCase"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName))}`, `lucide-${iconName}`, className),
            ...props
        }));
    Component.displayName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName);
    return Component;
};
;
 //# sourceMappingURL=createLucideIcon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>ArrowLeft)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m12 19-7-7 7-7",
            key: "1l729n"
        }
    ],
    [
        "path",
        {
            d: "M19 12H5",
            key: "x3x0zl"
        }
    ]
];
const ArrowLeft = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("arrow-left", __iconNode);
;
 //# sourceMappingURL=arrow-left.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeft>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ArrowLeft": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Save)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",
            key: "1c8476"
        }
    ],
    [
        "path",
        {
            d: "M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",
            key: "1ydtos"
        }
    ],
    [
        "path",
        {
            d: "M7 3v4a1 1 0 0 0 1 1h7",
            key: "t51u73"
        }
    ]
];
const Save = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("save", __iconNode);
;
 //# sourceMappingURL=save.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-client] (ecmascript) <export default as Save>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Save": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=_de4d6776._.js.map