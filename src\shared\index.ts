/**
 * 共享模块的统一导出
 * 提供类型、常量、工具函数的统一入口
 */

// ============================================================================
// 类型导出
// ============================================================================

export type {
  // 核心业务类型
  Task,
  TaskCategory,
  Priority,
  TaskStatus,
  Quadrant,
  
  // 时间相关类型
  TimeSlot,
  DailySchedule,
  ScoredTask,
  
  // 用户配置类型
  UserProfile,
  WorkHours,
  UserTimeConfig,
  FixedTimeSlot,
  CategoryPreferences,
  CategoryPreference,
  
  // 分析相关类型
  CategoryRatios,
  BalanceAnalysis,
  WeeklyStats,
  DailyStats,
  
  // 算法相关类型
  PostponedTaskAlert,
  AdjustmentResult,
  
  // API 类型
  ApiResponse,
  PaginatedResponse,
  
  // 事件类型
  CalendarEvent,
  
  // 错误类型
  AppError
} from './types/core';

// ============================================================================
// 常量导出
// ============================================================================

export {
  // 任务相关常量
  TASK_CATEGORIES,
  TASK_STATUSES,
  PRIORITY_LEVELS,
  QUADRANTS,
  
  // 时间相关常量
  TIME_CONSTANTS,
  WORK_DAYS,
  ALL_DAYS,
  DAY_NAMES_CN,
  
  // 默认配置
  DEFAULT_USER_CONFIG,
  
  // 算法常量
  ALGORITHM_CONSTANTS,
  
  // UI 常量
  UI_CONSTANTS,
  
  // 错误代码
  ERROR_CODES
} from './constants';

// ============================================================================
// 工具函数导出
// ============================================================================

export {
  // 日期工具
  formatDate,
  formatTime,
  formatDateTime,
  formatDuration,
  getStartOfDay,
  getEndOfDay,
  getTomorrow,
  getStartOfWeek,
  getEndOfWeek,
  isSameDay,
  isToday,
  isTomorrow,
  getDaysDifference,
  parseTimeToday,
  createDateTime,
  getDefaultDeadline,
  isTimeInRange,
  isTimeRangeOverlap,
  getMinutesBetween,
  isWorkday,
  getNextWorkday,
  getRelativeTime
} from './utils/dateUtils';

export {
  // 任务工具
  classifyQuadrant,
  getQuadrantDescription,
  getQuadrantTitle,
  getCategoryName,
  getCategoryColor,
  getCategoryIcon,
  getStatusName,
  getStatusColor,
  canStartTask,
  isTaskCompleted,
  isTaskInProgress,
  getPriorityName,
  getPriorityColor,
  filterTasksByCategory,
  filterTasksByStatus,
  filterTasksByQuadrant,
  getTodayTasks,
  sortTasksByPriority,
  sortTasksByDeadline,
  validateTask,
  isTaskDueSoon,
  isTaskOverdue,
  getTaskUrgencyDescription
} from './utils/taskUtils';

// ============================================================================
// 类型守卫和验证函数
// ============================================================================

/**
 * 检查是否为有效的任务分类
 */
export function isValidTaskCategory(value: any): value is TaskCategory {
  return Object.values(TASK_CATEGORIES).includes(value);
}

/**
 * 检查是否为有效的优先级
 */
export function isValidPriority(value: any): value is Priority {
  return typeof value === 'number' && value >= 1 && value <= 5;
}

/**
 * 检查是否为有效的任务状态
 */
export function isValidTaskStatus(value: any): value is TaskStatus {
  return Object.values(TASK_STATUSES).includes(value);
}

/**
 * 检查是否为有效的四象限
 */
export function isValidQuadrant(value: any): value is Quadrant {
  return typeof value === 'number' && value >= 1 && value <= 4;
}

// ============================================================================
// 错误处理工具
// ============================================================================

/**
 * 创建应用错误对象
 */
export function createAppError(
  code: string,
  message: string,
  details?: any
): AppError {
  return {
    code,
    message,
    details,
    timestamp: new Date()
  };
}

/**
 * 检查是否为应用错误
 */
export function isAppError(error: any): error is AppError {
  return error && 
         typeof error.code === 'string' && 
         typeof error.message === 'string' && 
         error.timestamp instanceof Date;
}

// ============================================================================
// 数据转换工具
// ============================================================================

/**
 * 将小时转换为分钟
 */
export function hoursToMinutes(hours: number): number {
  return Math.round(hours * TIME_CONSTANTS.MINUTES_PER_HOUR);
}

/**
 * 将分钟转换为小时
 */
export function minutesToHours(minutes: number): number {
  return minutes / TIME_CONSTANTS.MINUTES_PER_HOUR;
}

/**
 * 安全地解析 JSON
 */
export function safeJsonParse<T>(json: string, defaultValue: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return defaultValue;
  }
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  const cloned = {} as T;
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  
  return cloned;
}
