{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/lib/algorithms/planning.ts"], "sourcesContent": ["import { Task, ScoredTask, DailySchedule, TimeSlot } from '@/types';\r\n\r\nexport class PlanningAlgorithm {\r\n  /**\r\n   * 计算任务的综合分数\r\n   */\r\n  calculateTaskScore(task: Task): number {\r\n    // 基础分数：重要性权重0.6，紧急性权重0.4\r\n    const baseScore = task.importance * 0.6 + task.urgency * 0.4;\r\n    \r\n    // 分类加权\r\n    const categoryBonus = {\r\n      work: 0,\r\n      improvement: 2,\r\n      entertainment: 1\r\n    }[task.category];\r\n    \r\n    // 推迟惩罚：每推迟一次扣3分\r\n    const postponePenalty = task.postponeCount * 3;\r\n    \r\n    // 截止时间紧迫性加权\r\n    const deadlineBonus = this.calculateDeadlineUrgency(task.deadline);\r\n    \r\n    return baseScore + categoryBonus + postponePenalty + deadlineBonus;\r\n  }\r\n  \r\n  /**\r\n   * 根据截止时间计算紧迫性加权\r\n   */\r\n  private calculateDeadlineUrgency(deadline: Date): number {\r\n    const now = new Date();\r\n    const hoursUntilDeadline = (deadline.getTime() - now.getTime()) / (1000 * 60 * 60);\r\n    \r\n    if (hoursUntilDeadline < 0) return 10; // 已过期，最高优先级\r\n    if (hoursUntilDeadline < 2) return 8;  // 2小时内\r\n    if (hoursUntilDeadline < 6) return 6;  // 6小时内\r\n    if (hoursUntilDeadline < 24) return 4; // 24小时内\r\n    if (hoursUntilDeadline < 72) return 2; // 3天内\r\n    return 0; // 3天以上\r\n  }\r\n  \r\n  /**\r\n   * 根据重要性和紧急性确定四象限\r\n   */\r\n  classifyQuadrant(importance: number, urgency: number): 1 | 2 | 3 | 4 {\r\n    const isImportant = importance >= 4;\r\n    const isUrgent = urgency >= 4;\r\n    \r\n    if (isImportant && isUrgent) return 1;      // 重要且紧急\r\n    if (isImportant && !isUrgent) return 2;    // 重要不紧急\r\n    if (!isImportant && isUrgent) return 3;    // 不重要但紧急\r\n    return 4;                                   // 不重要不紧急\r\n  }\r\n  \r\n  /**\r\n   * 生成今日时间安排\r\n   */\r\n  generateDailySchedule(tasks: Task[], userTimeConfig?: any): DailySchedule {\r\n    // 1. 过滤今日需要处理的任务\r\n    const todayTasks = this.filterTodayTasks(tasks);\r\n\r\n    // 2. 计算分数并分类\r\n    const scoredTasks: ScoredTask[] = todayTasks\r\n      .map(task => ({\r\n        ...task,\r\n        score: this.calculateTaskScore(task),\r\n        quadrant: this.classifyQuadrant(task.importance, task.urgency)\r\n      }))\r\n      .sort((a, b) => {\r\n        // 先按象限排序，再按分数排序\r\n        if (a.quadrant !== b.quadrant) {\r\n          return a.quadrant - b.quadrant;\r\n        }\r\n        return b.score - a.score;\r\n      });\r\n\r\n    // 3. 生成时间段（使用新的基于任务类型的算法）\r\n    const timeSlots = this.generateTimeSlotsWithCategories(scoredTasks, userTimeConfig);\r\n\r\n    // 4. 计算总时长\r\n    const totalDuration = timeSlots.reduce((sum, slot) =>\r\n      sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0\r\n    );\r\n\r\n    return {\r\n      date: new Date(),\r\n      timeSlots,\r\n      totalTasks: todayTasks.length,\r\n      estimatedDuration: totalDuration\r\n    };\r\n  }\r\n  \r\n  /**\r\n   * 过滤今日需要处理的任务\r\n   */\r\n  private filterTodayTasks(tasks: Task[]): Task[] {\r\n    const today = new Date();\r\n    const tomorrow = new Date(today);\r\n    tomorrow.setDate(tomorrow.getDate() + 1);\r\n    \r\n    return tasks.filter(task => {\r\n      // 包含今日截止的任务和未完成的高优先级任务\r\n      const isToday = task.deadline <= tomorrow;\r\n      const isHighPriority = task.importance >= 4 || task.urgency >= 4;\r\n      const isPending = task.status === 'pending' || task.status === 'in-progress';\r\n      \r\n      return isPending && (isToday || isHighPriority);\r\n    });\r\n  }\r\n  \r\n  /**\r\n   * 基于任务类型生成时间段安排\r\n   */\r\n  private generateTimeSlotsWithCategories(tasks: ScoredTask[], userTimeConfig?: any): TimeSlot[] {\r\n    const timeSlots: TimeSlot[] = [];\r\n    const today = new Date();\r\n\r\n    // 使用默认配置如果没有提供用户配置\r\n    const defaultConfig = {\r\n      workStart: '09:00',\r\n      workEnd: '18:00',\r\n      categoryPreferences: {\r\n        work: { preferredTimes: ['09:00-12:00', '14:00-18:00'], maxDaily: 480 },\r\n        improvement: { preferredTimes: ['07:00-09:00', '19:00-21:00'], maxDaily: 120 },\r\n        entertainment: { preferredTimes: ['20:00-22:00'], maxDaily: 180 }\r\n      },\r\n      fixedSlots: [\r\n        { start: '12:00', end: '13:00', type: 'meal', label: '午餐时间' }\r\n      ]\r\n    };\r\n\r\n    const config = userTimeConfig || defaultConfig;\r\n\r\n    // 按任务类型分组\r\n    const tasksByCategory = {\r\n      work: tasks.filter(task => task.category === 'work'),\r\n      improvement: tasks.filter(task => task.category === 'improvement'),\r\n      entertainment: tasks.filter(task => task.category === 'entertainment')\r\n    };\r\n\r\n    // 为每种类型的任务安排时间\r\n    for (const [category, categoryTasks] of Object.entries(tasksByCategory)) {\r\n      if (categoryTasks.length === 0) continue;\r\n\r\n      const categoryPrefs = config.categoryPreferences[category as keyof typeof config.categoryPreferences];\r\n      if (!categoryPrefs) continue;\r\n\r\n      // 为该类型任务生成可用时间段\r\n      const availableSlots = this.generateAvailableSlots(categoryPrefs.preferredTimes, config.fixedSlots, today);\r\n\r\n      // 在可用时间段中安排任务\r\n      this.scheduleTasksInSlots(categoryTasks, availableSlots, timeSlots);\r\n    }\r\n\r\n    return timeSlots.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());\r\n  }\r\n\r\n  /**\r\n   * 生成时间段安排（保留原方法作为后备）\r\n   */\r\n  private generateTimeSlots(tasks: ScoredTask[], workHours: { start: string; end: string }): TimeSlot[] {\r\n    const timeSlots: TimeSlot[] = [];\r\n    const today = new Date();\r\n\r\n    // 解析工作时间\r\n    const [startHour, startMinute] = workHours.start.split(':').map(Number);\r\n    const [endHour, endMinute] = workHours.end.split(':').map(Number);\r\n\r\n    let currentTime = new Date(today);\r\n    currentTime.setHours(startHour, startMinute, 0, 0);\r\n\r\n    const workEndTime = new Date(today);\r\n    workEndTime.setHours(endHour, endMinute, 0, 0);\r\n\r\n    for (const task of tasks) {\r\n      // 检查是否还有足够的工作时间\r\n      const remainingWorkTime = workEndTime.getTime() - currentTime.getTime();\r\n      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒\r\n\r\n      if (remainingWorkTime < taskDuration) {\r\n        // 如果当天时间不够，跳过或安排到明天\r\n        continue;\r\n      }\r\n\r\n      const endTime = new Date(currentTime.getTime() + taskDuration);\r\n\r\n      timeSlots.push({\r\n        task,\r\n        startTime: new Date(currentTime),\r\n        endTime,\r\n        isFixed: false\r\n      });\r\n\r\n      // 更新当前时间，添加15分钟休息时间\r\n      currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);\r\n\r\n      // 如果超过工作时间，停止安排\r\n      if (currentTime >= workEndTime) {\r\n        break;\r\n      }\r\n    }\r\n\r\n    return timeSlots;\r\n  }\r\n  \r\n  /**\r\n   * 获取四象限的描述\r\n   */\r\n  getQuadrantDescription(quadrant: 1 | 2 | 3 | 4): string {\r\n    const descriptions = {\r\n      1: '重要且紧急 - 立即执行',\r\n      2: '重要不紧急 - 计划执行',\r\n      3: '不重要但紧急 - 委托处理',\r\n      4: '不重要不紧急 - 减少或删除'\r\n    };\r\n    return descriptions[quadrant];\r\n  }\r\n  \r\n  /**\r\n   * 生成可用时间段\r\n   */\r\n  private generateAvailableSlots(preferredTimes: string[], fixedSlots: any[], date: Date): { start: Date; end: Date }[] {\r\n    const availableSlots: { start: Date; end: Date }[] = [];\r\n\r\n    for (const timeRange of preferredTimes) {\r\n      const [startTime, endTime] = timeRange.split('-');\r\n      const [startHour, startMinute] = startTime.split(':').map(Number);\r\n      const [endHour, endMinute] = endTime.split(':').map(Number);\r\n\r\n      const slotStart = new Date(date);\r\n      slotStart.setHours(startHour, startMinute, 0, 0);\r\n\r\n      const slotEnd = new Date(date);\r\n      slotEnd.setHours(endHour, endMinute, 0, 0);\r\n\r\n      // 检查是否与固定时间段冲突\r\n      let hasConflict = false;\r\n      for (const fixedSlot of fixedSlots) {\r\n        const [fixedStartHour, fixedStartMinute] = fixedSlot.start.split(':').map(Number);\r\n        const [fixedEndHour, fixedEndMinute] = fixedSlot.end.split(':').map(Number);\r\n\r\n        const fixedStart = new Date(date);\r\n        fixedStart.setHours(fixedStartHour, fixedStartMinute, 0, 0);\r\n\r\n        const fixedEnd = new Date(date);\r\n        fixedEnd.setHours(fixedEndHour, fixedEndMinute, 0, 0);\r\n\r\n        // 检查时间段重叠\r\n        if (slotStart < fixedEnd && slotEnd > fixedStart) {\r\n          hasConflict = true;\r\n          break;\r\n        }\r\n      }\r\n\r\n      if (!hasConflict) {\r\n        availableSlots.push({ start: slotStart, end: slotEnd });\r\n      }\r\n    }\r\n\r\n    return availableSlots;\r\n  }\r\n\r\n  /**\r\n   * 在可用时间段中安排任务\r\n   */\r\n  private scheduleTasksInSlots(tasks: ScoredTask[], availableSlots: { start: Date; end: Date }[], timeSlots: TimeSlot[]): void {\r\n    let currentSlotIndex = 0;\r\n    let currentTime = availableSlots[0]?.start;\r\n\r\n    if (!currentTime) return;\r\n\r\n    for (const task of tasks) {\r\n      const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒\r\n\r\n      // 寻找合适的时间段\r\n      while (currentSlotIndex < availableSlots.length) {\r\n        const currentSlot = availableSlots[currentSlotIndex];\r\n        const remainingTime = currentSlot.end.getTime() - currentTime.getTime();\r\n\r\n        if (remainingTime >= taskDuration) {\r\n          // 在当前时间段安排任务\r\n          const endTime = new Date(currentTime.getTime() + taskDuration);\r\n\r\n          timeSlots.push({\r\n            task,\r\n            startTime: new Date(currentTime),\r\n            endTime,\r\n            isFixed: false\r\n          });\r\n\r\n          // 更新当前时间，添加15分钟休息时间\r\n          currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);\r\n          break;\r\n        } else {\r\n          // 移动到下一个时间段\r\n          currentSlotIndex++;\r\n          currentTime = availableSlots[currentSlotIndex]?.start;\r\n          if (!currentTime) break;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取任务建议\r\n   */\r\n  getTaskRecommendation(task: ScoredTask): string {\r\n    if (task.quadrant === 1) {\r\n      return '🔥 高优先级任务，建议立即处理';\r\n    } else if (task.quadrant === 2) {\r\n      return '📅 重要任务，建议合理安排时间';\r\n    } else if (task.quadrant === 3) {\r\n      return '⚡ 紧急但不重要，考虑委托或快速处理';\r\n    } else {\r\n      return '🤔 优先级较低，可以延后或删除';\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX;;GAEC,GACD,mBAAmB,IAAU,EAAU;QACrC,yBAAyB;QACzB,MAAM,YAAY,KAAK,UAAU,GAAG,MAAM,KAAK,OAAO,GAAG;QAEzD,OAAO;QACP,MAAM,gBAAgB;YACpB,MAAM;YACN,aAAa;YACb,eAAe;QACjB,CAAC,CAAC,KAAK,QAAQ,CAAC;QAEhB,gBAAgB;QAChB,MAAM,kBAAkB,KAAK,aAAa,GAAG;QAE7C,YAAY;QACZ,MAAM,gBAAgB,IAAI,CAAC,wBAAwB,CAAC,KAAK,QAAQ;QAEjE,OAAO,YAAY,gBAAgB,kBAAkB;IACvD;IAEA;;GAEC,GACD,AAAQ,yBAAyB,QAAc,EAAU;QACvD,MAAM,MAAM,IAAI;QAChB,MAAM,qBAAqB,CAAC,SAAS,OAAO,KAAK,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEjF,IAAI,qBAAqB,GAAG,OAAO,IAAI,YAAY;QACnD,IAAI,qBAAqB,GAAG,OAAO,GAAI,OAAO;QAC9C,IAAI,qBAAqB,GAAG,OAAO,GAAI,OAAO;QAC9C,IAAI,qBAAqB,IAAI,OAAO,GAAG,QAAQ;QAC/C,IAAI,qBAAqB,IAAI,OAAO,GAAG,MAAM;QAC7C,OAAO,GAAG,OAAO;IACnB;IAEA;;GAEC,GACD,iBAAiB,UAAkB,EAAE,OAAe,EAAiB;QACnE,MAAM,cAAc,cAAc;QAClC,MAAM,WAAW,WAAW;QAE5B,IAAI,eAAe,UAAU,OAAO,GAAQ,QAAQ;QACpD,IAAI,eAAe,CAAC,UAAU,OAAO,GAAM,QAAQ;QACnD,IAAI,CAAC,eAAe,UAAU,OAAO,GAAM,SAAS;QACpD,OAAO,GAAqC,SAAS;IACvD;IAEA;;GAEC,GACD,sBAAsB,KAAa,EAAE,cAAoB,EAAiB;QACxE,iBAAiB;QACjB,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC;QAEzC,aAAa;QACb,MAAM,cAA4B,WAC/B,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,GAAG,IAAI;gBACP,OAAO,IAAI,CAAC,kBAAkB,CAAC;gBAC/B,UAAU,IAAI,CAAC,gBAAgB,CAAC,KAAK,UAAU,EAAE,KAAK,OAAO;YAC/D,CAAC,GACA,IAAI,CAAC,CAAC,GAAG;YACR,gBAAgB;YAChB,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;gBAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAChC;YACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;QAC1B;QAEF,0BAA0B;QAC1B,MAAM,YAAY,IAAI,CAAC,+BAA+B,CAAC,aAAa;QAEpE,WAAW;QACX,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAC,KAAK,OAC3C,MAAM,CAAC,KAAK,OAAO,CAAC,OAAO,KAAK,KAAK,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG;QAG3E,OAAO;YACL,MAAM,IAAI;YACV;YACA,YAAY,WAAW,MAAM;YAC7B,mBAAmB;QACrB;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAiB,KAAa,EAAU;QAC9C,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QAEtC,OAAO,MAAM,MAAM,CAAC,CAAA;YAClB,uBAAuB;YACvB,MAAM,UAAU,KAAK,QAAQ,IAAI;YACjC,MAAM,iBAAiB,KAAK,UAAU,IAAI,KAAK,KAAK,OAAO,IAAI;YAC/D,MAAM,YAAY,KAAK,MAAM,KAAK,aAAa,KAAK,MAAM,KAAK;YAE/D,OAAO,aAAa,CAAC,WAAW,cAAc;QAChD;IACF;IAEA;;GAEC,GACD,AAAQ,gCAAgC,KAAmB,EAAE,cAAoB,EAAc;QAC7F,MAAM,YAAwB,EAAE;QAChC,MAAM,QAAQ,IAAI;QAElB,mBAAmB;QACnB,MAAM,gBAAgB;YACpB,WAAW;YACX,SAAS;YACT,qBAAqB;gBACnB,MAAM;oBAAE,gBAAgB;wBAAC;wBAAe;qBAAc;oBAAE,UAAU;gBAAI;gBACtE,aAAa;oBAAE,gBAAgB;wBAAC;wBAAe;qBAAc;oBAAE,UAAU;gBAAI;gBAC7E,eAAe;oBAAE,gBAAgB;wBAAC;qBAAc;oBAAE,UAAU;gBAAI;YAClE;YACA,YAAY;gBACV;oBAAE,OAAO;oBAAS,KAAK;oBAAS,MAAM;oBAAQ,OAAO;gBAAO;aAC7D;QACH;QAEA,MAAM,SAAS,kBAAkB;QAEjC,UAAU;QACV,MAAM,kBAAkB;YACtB,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YAC7C,aAAa,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;YACpD,eAAe,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;QACxD;QAEA,eAAe;QACf,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,iBAAkB;YACvE,IAAI,cAAc,MAAM,KAAK,GAAG;YAEhC,MAAM,gBAAgB,OAAO,mBAAmB,CAAC,SAAoD;YACrG,IAAI,CAAC,eAAe;YAEpB,gBAAgB;YAChB,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,cAAc,cAAc,EAAE,OAAO,UAAU,EAAE;YAEpG,cAAc;YACd,IAAI,CAAC,oBAAoB,CAAC,eAAe,gBAAgB;QAC3D;QAEA,OAAO,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;IAC7E;IAEA;;GAEC,GACD,AAAQ,kBAAkB,KAAmB,EAAE,SAAyC,EAAc;QACpG,MAAM,YAAwB,EAAE;QAChC,MAAM,QAAQ,IAAI;QAElB,SAAS;QACT,MAAM,CAAC,WAAW,YAAY,GAAG,UAAU,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAChE,MAAM,CAAC,SAAS,UAAU,GAAG,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAE1D,IAAI,cAAc,IAAI,KAAK;QAC3B,YAAY,QAAQ,CAAC,WAAW,aAAa,GAAG;QAEhD,MAAM,cAAc,IAAI,KAAK;QAC7B,YAAY,QAAQ,CAAC,SAAS,WAAW,GAAG;QAE5C,KAAK,MAAM,QAAQ,MAAO;YACxB,gBAAgB;YAChB,MAAM,oBAAoB,YAAY,OAAO,KAAK,YAAY,OAAO;YACrE,MAAM,eAAe,CAAC,KAAK,iBAAiB,IAAI,EAAE,IAAI,KAAK,MAAM,QAAQ;YAEzE,IAAI,oBAAoB,cAAc;gBAEpC;YACF;YAEA,MAAM,UAAU,IAAI,KAAK,YAAY,OAAO,KAAK;YAEjD,UAAU,IAAI,CAAC;gBACb;gBACA,WAAW,IAAI,KAAK;gBACpB;gBACA,SAAS;YACX;YAEA,oBAAoB;YACpB,cAAc,IAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,KAAK;YAErD,gBAAgB;YAChB,IAAI,eAAe,aAAa;gBAC9B;YACF;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,uBAAuB,QAAuB,EAAU;QACtD,MAAM,eAAe;YACnB,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;QACL;QACA,OAAO,YAAY,CAAC,SAAS;IAC/B;IAEA;;GAEC,GACD,AAAQ,uBAAuB,cAAwB,EAAE,UAAiB,EAAE,IAAU,EAAgC;QACpH,MAAM,iBAA+C,EAAE;QAEvD,KAAK,MAAM,aAAa,eAAgB;YACtC,MAAM,CAAC,WAAW,QAAQ,GAAG,UAAU,KAAK,CAAC;YAC7C,MAAM,CAAC,WAAW,YAAY,GAAG,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC;YAC1D,MAAM,CAAC,SAAS,UAAU,GAAG,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;YAEpD,MAAM,YAAY,IAAI,KAAK;YAC3B,UAAU,QAAQ,CAAC,WAAW,aAAa,GAAG;YAE9C,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,QAAQ,CAAC,SAAS,WAAW,GAAG;YAExC,eAAe;YACf,IAAI,cAAc;YAClB,KAAK,MAAM,aAAa,WAAY;gBAClC,MAAM,CAAC,gBAAgB,iBAAiB,GAAG,UAAU,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;gBAC1E,MAAM,CAAC,cAAc,eAAe,GAAG,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;gBAEpE,MAAM,aAAa,IAAI,KAAK;gBAC5B,WAAW,QAAQ,CAAC,gBAAgB,kBAAkB,GAAG;gBAEzD,MAAM,WAAW,IAAI,KAAK;gBAC1B,SAAS,QAAQ,CAAC,cAAc,gBAAgB,GAAG;gBAEnD,UAAU;gBACV,IAAI,YAAY,YAAY,UAAU,YAAY;oBAChD,cAAc;oBACd;gBACF;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,eAAe,IAAI,CAAC;oBAAE,OAAO;oBAAW,KAAK;gBAAQ;YACvD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,qBAAqB,KAAmB,EAAE,cAA4C,EAAE,SAAqB,EAAQ;QAC3H,IAAI,mBAAmB;QACvB,IAAI,cAAc,cAAc,CAAC,EAAE,EAAE;QAErC,IAAI,CAAC,aAAa;QAElB,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,eAAe,CAAC,KAAK,iBAAiB,IAAI,EAAE,IAAI,KAAK,MAAM,QAAQ;YAEzE,WAAW;YACX,MAAO,mBAAmB,eAAe,MAAM,CAAE;gBAC/C,MAAM,cAAc,cAAc,CAAC,iBAAiB;gBACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC,OAAO,KAAK,YAAY,OAAO;gBAErE,IAAI,iBAAiB,cAAc;oBACjC,aAAa;oBACb,MAAM,UAAU,IAAI,KAAK,YAAY,OAAO,KAAK;oBAEjD,UAAU,IAAI,CAAC;wBACb;wBACA,WAAW,IAAI,KAAK;wBACpB;wBACA,SAAS;oBACX;oBAEA,oBAAoB;oBACpB,cAAc,IAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,KAAK;oBACrD;gBACF,OAAO;oBACL,YAAY;oBACZ;oBACA,cAAc,cAAc,CAAC,iBAAiB,EAAE;oBAChD,IAAI,CAAC,aAAa;gBACpB;YACF;QACF;IACF;IAEA;;GAEC,GACD,sBAAsB,IAAgB,EAAU;QAC9C,IAAI,KAAK,QAAQ,KAAK,GAAG;YACvB,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,GAAG;YAC9B,OAAO;QACT,OAAO,IAAI,KAAK,QAAQ,KAAK,GAAG;YAC9B,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/lib/algorithms/balance.ts"], "sourcesContent": ["import { CategoryRatios, BalanceAnalysis, DailyStats } from '@/types';\r\nimport { supabase } from '@/lib/supabase';\r\n\r\nexport class BalanceAlgorithm {\r\n  // 理想的时间分配比例\r\n  private readonly IDEAL_RATIOS: CategoryRatios = {\r\n    work: 0.6,\r\n    improvement: 0.25,\r\n    entertainment: 0.15\r\n  };\r\n  \r\n  /**\r\n   * 分析用户的生活平衡状况\r\n   */\r\n  async analyzeWeeklyBalance(userId: string): Promise<BalanceAnalysis> {\r\n    try {\r\n      // 获取最近7天的统计数据\r\n      const stats = await this.getDailyStats(userId, 7);\r\n      \r\n      if (stats.length === 0) {\r\n        return {\r\n          workRatio: 0,\r\n          improvementRatio: 0,\r\n          entertainmentRatio: 0,\r\n          balanceScore: 0,\r\n          recommendation: \"开始记录你的任务来获得生活平衡分析 📊\"\r\n        };\r\n      }\r\n      \r\n      // 计算总时间和各分类时间\r\n      const totalTime = stats.reduce((sum, day) => \r\n        sum + day.workTime + day.improvementTime + day.entertainmentTime, 0\r\n      );\r\n      \r\n      if (totalTime === 0) {\r\n        return {\r\n          workRatio: 0,\r\n          improvementRatio: 0,\r\n          entertainmentRatio: 0,\r\n          balanceScore: 0,\r\n          recommendation: \"还没有完成任务记录，开始你的第一个任务吧！ 🚀\"\r\n        };\r\n      }\r\n      \r\n      // 计算各分类的时间比例\r\n      const ratios: CategoryRatios = {\r\n        work: stats.reduce((sum, day) => sum + day.workTime, 0) / totalTime,\r\n        improvement: stats.reduce((sum, day) => sum + day.improvementTime, 0) / totalTime,\r\n        entertainment: stats.reduce((sum, day) => sum + day.entertainmentTime, 0) / totalTime\r\n      };\r\n      \r\n      // 计算平衡分数\r\n      const balanceScore = this.calculateBalanceScore(ratios);\r\n      \r\n      // 生成建议\r\n      const recommendation = this.generateRecommendation(ratios, stats);\r\n      \r\n      return {\r\n        workRatio: ratios.work,\r\n        improvementRatio: ratios.improvement,\r\n        entertainmentRatio: ratios.entertainment,\r\n        balanceScore,\r\n        recommendation\r\n      };\r\n    } catch (error) {\r\n      console.error('Error analyzing balance:', error);\r\n      throw new Error('Failed to analyze balance');\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 计算生活平衡分数 (0-100)\r\n   */\r\n  private calculateBalanceScore(ratios: CategoryRatios): number {\r\n    let score = 100;\r\n    \r\n    // 计算每个分类与理想比例的偏差\r\n    Object.keys(this.IDEAL_RATIOS).forEach(category => {\r\n      const ideal = this.IDEAL_RATIOS[category as keyof CategoryRatios];\r\n      const actual = ratios[category as keyof CategoryRatios];\r\n      const deviation = Math.abs(ideal - actual);\r\n      \r\n      // 偏差越大，扣分越多\r\n      score -= deviation * 100;\r\n    });\r\n    \r\n    return Math.max(0, Math.round(score));\r\n  }\r\n  \r\n  /**\r\n   * 生成个性化建议\r\n   */\r\n  private generateRecommendation(ratios: CategoryRatios, stats: DailyStats[]): string {\r\n    const recommendations: string[] = [];\r\n    \r\n    // 分析工作时间\r\n    if (ratios.work > 0.8) {\r\n      recommendations.push(\"⚠️ 工作时间过长，建议增加休息和娱乐时间\");\r\n    } else if (ratios.work < 0.3) {\r\n      recommendations.push(\"💼 工作时间较少，可以适当增加工作或学习时间\");\r\n    }\r\n    \r\n    // 分析提升时间\r\n    if (ratios.improvement < 0.1) {\r\n      recommendations.push(\"📚 建议安排一些学习或自我提升的活动\");\r\n    } else if (ratios.improvement > 0.4) {\r\n      recommendations.push(\"🎯 学习时间充足，注意劳逸结合\");\r\n    }\r\n    \r\n    // 分析娱乐时间\r\n    if (ratios.entertainment < 0.05) {\r\n      recommendations.push(\"🎮 需要更多的放松和娱乐时间\");\r\n    } else if (ratios.entertainment > 0.3) {\r\n      recommendations.push(\"⏰ 娱乐时间较多，可以适当增加工作或学习\");\r\n    }\r\n    \r\n    // 分析连续性\r\n    const recentDays = stats.slice(-3); // 最近3天\r\n    const hasConsistentWork = recentDays.every(day => day.workTime > 0);\r\n    const hasConsistentImprovement = recentDays.every(day => day.improvementTime > 0);\r\n    \r\n    if (!hasConsistentWork) {\r\n      recommendations.push(\"🔄 建议保持每日工作的连续性\");\r\n    }\r\n    \r\n    if (!hasConsistentImprovement) {\r\n      recommendations.push(\"📈 建议每天安排一些自我提升时间\");\r\n    }\r\n    \r\n    // 如果没有特别的建议，给出正面反馈\r\n    if (recommendations.length === 0) {\r\n      const score = this.calculateBalanceScore(ratios);\r\n      if (score >= 80) {\r\n        return \"✨ 生活平衡状态优秀，继续保持！\";\r\n      } else if (score >= 60) {\r\n        return \"👍 生活平衡状态良好，可以微调优化\";\r\n      } else {\r\n        return \"🎯 生活平衡有改善空间，建议关注时间分配\";\r\n      }\r\n    }\r\n    \r\n    return recommendations.join(\" • \");\r\n  }\r\n  \r\n  /**\r\n   * 获取用户的每日统计数据\r\n   */\r\n  private async getDailyStats(userId: string, days: number): Promise<DailyStats[]> {\r\n    const startDate = new Date();\r\n    startDate.setDate(startDate.getDate() - days);\r\n    \r\n    const { data, error } = await supabase\r\n      .from('daily_stats')\r\n      .select('*')\r\n      .eq('user_id', userId)\r\n      .gte('date', startDate.toISOString().split('T')[0])\r\n      .order('date', { ascending: true });\r\n    \r\n    if (error) {\r\n      console.error('Error fetching daily stats:', error);\r\n      return [];\r\n    }\r\n    \r\n    return data.map(stat => ({\r\n      id: stat.id,\r\n      userId: stat.user_id,\r\n      date: new Date(stat.date),\r\n      workTime: stat.work_time,\r\n      improvementTime: stat.improvement_time,\r\n      entertainmentTime: stat.entertainment_time,\r\n      tasksCompleted: stat.tasks_completed,\r\n      tasksPostponed: stat.tasks_postponed,\r\n      balanceScore: stat.balance_score || 0\r\n    }));\r\n  }\r\n  \r\n  /**\r\n   * 更新今日统计数据\r\n   */\r\n  async updateTodayStats(userId: string, category: string, timeSpent: number): Promise<void> {\r\n    const today = new Date().toISOString().split('T')[0];\r\n    \r\n    try {\r\n      // 获取今日统计\r\n      const { data: existing } = await supabase\r\n        .from('daily_stats')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n        .eq('date', today)\r\n        .single();\r\n      \r\n      const updateData: any = {};\r\n      \r\n      if (category === 'work') {\r\n        updateData.work_time = (existing?.work_time || 0) + timeSpent;\r\n      } else if (category === 'improvement') {\r\n        updateData.improvement_time = (existing?.improvement_time || 0) + timeSpent;\r\n      } else if (category === 'entertainment') {\r\n        updateData.entertainment_time = (existing?.entertainment_time || 0) + timeSpent;\r\n      }\r\n      \r\n      if (existing) {\r\n        // 更新现有记录\r\n        await supabase\r\n          .from('daily_stats')\r\n          .update(updateData)\r\n          .eq('id', existing.id);\r\n      } else {\r\n        // 创建新记录\r\n        await supabase\r\n          .from('daily_stats')\r\n          .insert({\r\n            user_id: userId,\r\n            date: today,\r\n            ...updateData\r\n          });\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating daily stats:', error);\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 获取平衡状态的颜色指示\r\n   */\r\n  getBalanceStatusColor(score: number): string {\r\n    if (score >= 80) return 'text-green-600';\r\n    if (score >= 60) return 'text-yellow-600';\r\n    if (score >= 40) return 'text-orange-600';\r\n    return 'text-red-600';\r\n  }\r\n  \r\n  /**\r\n   * 获取平衡状态的描述\r\n   */\r\n  getBalanceStatusText(score: number): string {\r\n    if (score >= 80) return '优秀';\r\n    if (score >= 60) return '良好';\r\n    if (score >= 40) return '一般';\r\n    return '需要改善';\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM;IACX,YAAY;IACK,eAA+B;QAC9C,MAAM;QACN,aAAa;QACb,eAAe;IACjB,EAAE;IAEF;;GAEC,GACD,MAAM,qBAAqB,MAAc,EAA4B;QACnE,IAAI;YACF,cAAc;YACd,MAAM,QAAQ,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ;YAE/C,IAAI,MAAM,MAAM,KAAK,GAAG;gBACtB,OAAO;oBACL,WAAW;oBACX,kBAAkB;oBAClB,oBAAoB;oBACpB,cAAc;oBACd,gBAAgB;gBAClB;YACF;YAEA,cAAc;YACd,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,MACnC,MAAM,IAAI,QAAQ,GAAG,IAAI,eAAe,GAAG,IAAI,iBAAiB,EAAE;YAGpE,IAAI,cAAc,GAAG;gBACnB,OAAO;oBACL,WAAW;oBACX,kBAAkB;oBAClB,oBAAoB;oBACpB,cAAc;oBACd,gBAAgB;gBAClB;YACF;YAEA,aAAa;YACb,MAAM,SAAyB;gBAC7B,MAAM,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,QAAQ,EAAE,KAAK;gBAC1D,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,eAAe,EAAE,KAAK;gBACxE,eAAe,MAAM,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,iBAAiB,EAAE,KAAK;YAC9E;YAEA,SAAS;YACT,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;YAEhD,OAAO;YACP,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,QAAQ;YAE3D,OAAO;gBACL,WAAW,OAAO,IAAI;gBACtB,kBAAkB,OAAO,WAAW;gBACpC,oBAAoB,OAAO,aAAa;gBACxC;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,MAAsB,EAAU;QAC5D,IAAI,QAAQ;QAEZ,iBAAiB;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;YACrC,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,SAAiC;YACjE,MAAM,SAAS,MAAM,CAAC,SAAiC;YACvD,MAAM,YAAY,KAAK,GAAG,CAAC,QAAQ;YAEnC,YAAY;YACZ,SAAS,YAAY;QACvB;QAEA,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC;IAChC;IAEA;;GAEC,GACD,AAAQ,uBAAuB,MAAsB,EAAE,KAAmB,EAAU;QAClF,MAAM,kBAA4B,EAAE;QAEpC,SAAS;QACT,IAAI,OAAO,IAAI,GAAG,KAAK;YACrB,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,IAAI,GAAG,KAAK;YAC5B,gBAAgB,IAAI,CAAC;QACvB;QAEA,SAAS;QACT,IAAI,OAAO,WAAW,GAAG,KAAK;YAC5B,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,WAAW,GAAG,KAAK;YACnC,gBAAgB,IAAI,CAAC;QACvB;QAEA,SAAS;QACT,IAAI,OAAO,aAAa,GAAG,MAAM;YAC/B,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,OAAO,aAAa,GAAG,KAAK;YACrC,gBAAgB,IAAI,CAAC;QACvB;QAEA,QAAQ;QACR,MAAM,aAAa,MAAM,KAAK,CAAC,CAAC,IAAI,OAAO;QAC3C,MAAM,oBAAoB,WAAW,KAAK,CAAC,CAAA,MAAO,IAAI,QAAQ,GAAG;QACjE,MAAM,2BAA2B,WAAW,KAAK,CAAC,CAAA,MAAO,IAAI,eAAe,GAAG;QAE/E,IAAI,CAAC,mBAAmB;YACtB,gBAAgB,IAAI,CAAC;QACvB;QAEA,IAAI,CAAC,0BAA0B;YAC7B,gBAAgB,IAAI,CAAC;QACvB;QAEA,mBAAmB;QACnB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,MAAM,QAAQ,IAAI,CAAC,qBAAqB,CAAC;YACzC,IAAI,SAAS,IAAI;gBACf,OAAO;YACT,OAAO,IAAI,SAAS,IAAI;gBACtB,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QAEA,OAAO,gBAAgB,IAAI,CAAC;IAC9B;IAEA;;GAEC,GACD,MAAc,cAAc,MAAc,EAAE,IAAY,EAAyB;QAC/E,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,GAAG,CAAC,QAAQ,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACjD,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,IAAI,KAAK,EAAE;gBACX,QAAQ,KAAK,OAAO;gBACpB,MAAM,IAAI,KAAK,KAAK,IAAI;gBACxB,UAAU,KAAK,SAAS;gBACxB,iBAAiB,KAAK,gBAAgB;gBACtC,mBAAmB,KAAK,kBAAkB;gBAC1C,gBAAgB,KAAK,eAAe;gBACpC,gBAAgB,KAAK,eAAe;gBACpC,cAAc,KAAK,aAAa,IAAI;YACtC,CAAC;IACH;IAEA;;GAEC,GACD,MAAM,iBAAiB,MAAc,EAAE,QAAgB,EAAE,SAAiB,EAAiB;QACzF,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAEpD,IAAI;YACF,SAAS;YACT,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACtC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ,OACX,MAAM;YAET,MAAM,aAAkB,CAAC;YAEzB,IAAI,aAAa,QAAQ;gBACvB,WAAW,SAAS,GAAG,CAAC,UAAU,aAAa,CAAC,IAAI;YACtD,OAAO,IAAI,aAAa,eAAe;gBACrC,WAAW,gBAAgB,GAAG,CAAC,UAAU,oBAAoB,CAAC,IAAI;YACpE,OAAO,IAAI,aAAa,iBAAiB;gBACvC,WAAW,kBAAkB,GAAG,CAAC,UAAU,sBAAsB,CAAC,IAAI;YACxE;YAEA,IAAI,UAAU;gBACZ,SAAS;gBACT,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,eACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,SAAS,EAAE;YACzB,OAAO;gBACL,QAAQ;gBACR,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,eACL,MAAM,CAAC;oBACN,SAAS;oBACT,MAAM;oBACN,GAAG,UAAU;gBACf;YACJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA;;GAEC,GACD,sBAAsB,KAAa,EAAU;QAC3C,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,KAAa,EAAU;QAC1C,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/lib/algorithms/fix.ts"], "sourcesContent": ["import { Task, PostponedTaskAlert } from '@/types';\r\nimport { supabase } from '@/lib/supabase';\r\n\r\nexport class FixAlgorithm {\r\n  // 推迟次数阈值\r\n  private readonly POSTPONE_THRESHOLDS = {\r\n    low: 1,\r\n    medium: 3,\r\n    high: 5,\r\n    critical: 8\r\n  };\r\n  \r\n  /**\r\n   * 分析需要修复的推迟任务\r\n   */\r\n  async analyzePostponedTasks(userId: string): Promise<PostponedTaskAlert[]> {\r\n    try {\r\n      // 获取所有推迟的任务\r\n      const postponedTasks = await this.getPostponedTasks(userId);\r\n      \r\n      return postponedTasks.map(task => {\r\n        const urgencyLevel = this.calculateUrgencyLevel(task);\r\n        const suggestion = this.generateFixSuggestion(task);\r\n        const daysSinceCreated = this.calculateDaysSince(task.createdAt);\r\n        \r\n        return {\r\n          task,\r\n          postponeCount: task.postponeCount,\r\n          daysSinceCreated,\r\n          urgencyLevel,\r\n          suggestion,\r\n          shouldAlert: urgencyLevel !== 'low'\r\n        };\r\n      }).filter(alert => alert.shouldAlert); // 只返回需要提醒的任务\r\n    } catch (error) {\r\n      console.error('Error analyzing postponed tasks:', error);\r\n      return [];\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 计算任务的紧急程度\r\n   */\r\n  private calculateUrgencyLevel(task: Task): 'low' | 'medium' | 'high' | 'critical' {\r\n    const { postponeCount, deadline } = task;\r\n    const daysSinceDeadline = this.calculateDaysSince(deadline);\r\n    const daysSinceCreated = this.calculateDaysSince(task.createdAt);\r\n    \r\n    // 综合考虑推迟次数、截止时间和创建时间\r\n    let urgencyScore = 0;\r\n    \r\n    // 推迟次数评分\r\n    if (postponeCount >= this.POSTPONE_THRESHOLDS.critical) urgencyScore += 4;\r\n    else if (postponeCount >= this.POSTPONE_THRESHOLDS.high) urgencyScore += 3;\r\n    else if (postponeCount >= this.POSTPONE_THRESHOLDS.medium) urgencyScore += 2;\r\n    else if (postponeCount >= this.POSTPONE_THRESHOLDS.low) urgencyScore += 1;\r\n    \r\n    // 截止时间评分\r\n    if (daysSinceDeadline > 0) urgencyScore += 3; // 已过期\r\n    else if (daysSinceDeadline > -1) urgencyScore += 2; // 1天内到期\r\n    else if (daysSinceDeadline > -3) urgencyScore += 1; // 3天内到期\r\n    \r\n    // 创建时间评分（任务存在时间过长）\r\n    if (daysSinceCreated > 14) urgencyScore += 2; // 超过2周\r\n    else if (daysSinceCreated > 7) urgencyScore += 1; // 超过1周\r\n    \r\n    // 重要性和紧急性加权\r\n    if (task.importance >= 4) urgencyScore += 1;\r\n    if (task.urgency >= 4) urgencyScore += 1;\r\n    \r\n    // 根据总分确定紧急程度\r\n    if (urgencyScore >= 7) return 'critical';\r\n    if (urgencyScore >= 5) return 'high';\r\n    if (urgencyScore >= 3) return 'medium';\r\n    return 'low';\r\n  }\r\n  \r\n  /**\r\n   * 生成修复建议\r\n   */\r\n  private generateFixSuggestion(task: Task): string {\r\n    const urgencyLevel = this.calculateUrgencyLevel(task);\r\n    const { postponeCount, category, estimatedDuration } = task;\r\n    \r\n    // 基于紧急程度的基础建议\r\n    const baseSuggestions = {\r\n      critical: \"🚨 紧急处理：这个任务已经严重延期，建议立即处理或重新评估其必要性\",\r\n      high: \"⚠️ 重点关注：建议将任务分解为更小的部分，或调整截止时间\",\r\n      medium: \"💡 优化建议：可以设置更具体的时间安排或降低任务难度\",\r\n      low: \"📝 轻微提醒：建议适当调整任务优先级或时间安排\"\r\n    };\r\n    \r\n    let suggestion = baseSuggestions[urgencyLevel];\r\n    \r\n    // 基于推迟次数的具体建议\r\n    if (postponeCount >= 5) {\r\n      suggestion += \"\\n• 考虑将任务分解为5-10分钟的小任务\";\r\n    } else if (postponeCount >= 3) {\r\n      suggestion += \"\\n• 尝试番茄工作法，专注25分钟\";\r\n    }\r\n    \r\n    // 基于任务时长的建议\r\n    if (estimatedDuration > 120) { // 超过2小时\r\n      suggestion += \"\\n• 任务时间较长，建议分解为多个子任务\";\r\n    }\r\n    \r\n    // 基于分类的建议\r\n    if (category === 'work') {\r\n      suggestion += \"\\n• 工作任务：考虑在精力最好的时间段处理\";\r\n    } else if (category === 'improvement') {\r\n      suggestion += \"\\n• 提升任务：可以设置学习奖励机制\";\r\n    } else if (category === 'entertainment') {\r\n      suggestion += \"\\n• 娱乐任务：确保这确实是你想要的放松方式\";\r\n    }\r\n    \r\n    return suggestion;\r\n  }\r\n  \r\n  /**\r\n   * 获取推迟的任务\r\n   */\r\n  private async getPostponedTasks(userId: string): Promise<Task[]> {\r\n    const { data, error } = await supabase\r\n      .from('tasks')\r\n      .select('*')\r\n      .eq('user_id', userId)\r\n      .gt('postpone_count', 0)\r\n      .in('status', ['pending', 'postponed'])\r\n      .order('postpone_count', { ascending: false });\r\n    \r\n    if (error) {\r\n      console.error('Error fetching postponed tasks:', error);\r\n      return [];\r\n    }\r\n    \r\n    return data.map(task => ({\r\n      id: task.id,\r\n      userId: task.user_id,\r\n      title: task.title,\r\n      description: task.description,\r\n      category: task.category,\r\n      importance: task.importance,\r\n      urgency: task.urgency,\r\n      deadline: new Date(task.deadline),\r\n      estimatedDuration: task.estimated_duration,\r\n      status: task.status,\r\n      postponeCount: task.postpone_count,\r\n      createdAt: new Date(task.created_at),\r\n      updatedAt: new Date(task.updated_at)\r\n    }));\r\n  }\r\n  \r\n  /**\r\n   * 计算距离某个日期的天数\r\n   */\r\n  private calculateDaysSince(date: Date): number {\r\n    const now = new Date();\r\n    const diffTime = now.getTime() - date.getTime();\r\n    return Math.floor(diffTime / (1000 * 60 * 60 * 24));\r\n  }\r\n  \r\n  /**\r\n   * 推迟任务\r\n   */\r\n  async postponeTask(taskId: string, reason?: string): Promise<void> {\r\n    try {\r\n      // 获取当前任务\r\n      const { data: task, error: fetchError } = await supabase\r\n        .from('tasks')\r\n        .select('postpone_count')\r\n        .eq('id', taskId)\r\n        .single();\r\n      \r\n      if (fetchError) throw fetchError;\r\n      \r\n      // 更新推迟次数\r\n      const { error: updateError } = await supabase\r\n        .from('tasks')\r\n        .update({\r\n          postpone_count: (task.postpone_count || 0) + 1,\r\n          status: 'postponed',\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', taskId);\r\n      \r\n      if (updateError) throw updateError;\r\n      \r\n      // 记录推迟历史（如果有历史表的话）\r\n      // 这里可以扩展记录推迟原因等信息\r\n      \r\n    } catch (error) {\r\n      console.error('Error postponing task:', error);\r\n      throw new Error('Failed to postpone task');\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 重置任务的推迟状态\r\n   */\r\n  async resetTaskPostponeStatus(taskId: string): Promise<void> {\r\n    try {\r\n      const { error } = await supabase\r\n        .from('tasks')\r\n        .update({\r\n          postpone_count: 0,\r\n          status: 'pending',\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', taskId);\r\n      \r\n      if (error) throw error;\r\n    } catch (error) {\r\n      console.error('Error resetting task postpone status:', error);\r\n      throw new Error('Failed to reset task postpone status');\r\n    }\r\n  }\r\n  \r\n  /**\r\n   * 获取推迟统计信息\r\n   */\r\n  async getPostponeStats(userId: string): Promise<{\r\n    totalPostponedTasks: number;\r\n    averagePostponeCount: number;\r\n    mostPostponedCategory: string;\r\n  }> {\r\n    try {\r\n      const { data, error } = await supabase\r\n        .from('tasks')\r\n        .select('postpone_count, category')\r\n        .eq('user_id', userId)\r\n        .gt('postpone_count', 0);\r\n      \r\n      if (error) throw error;\r\n      \r\n      if (!data || data.length === 0) {\r\n        return {\r\n          totalPostponedTasks: 0,\r\n          averagePostponeCount: 0,\r\n          mostPostponedCategory: 'work'\r\n        };\r\n      }\r\n      \r\n      const totalPostponedTasks = data.length;\r\n      const averagePostponeCount = data.reduce((sum, task) => sum + task.postpone_count, 0) / totalPostponedTasks;\r\n      \r\n      // 统计各分类的推迟次数\r\n      const categoryStats = data.reduce((acc, task) => {\r\n        acc[task.category] = (acc[task.category] || 0) + task.postpone_count;\r\n        return acc;\r\n      }, {} as Record<string, number>);\r\n      \r\n      const mostPostponedCategory = Object.keys(categoryStats).reduce((a, b) => \r\n        categoryStats[a] > categoryStats[b] ? a : b\r\n      );\r\n      \r\n      return {\r\n        totalPostponedTasks,\r\n        averagePostponeCount: Math.round(averagePostponeCount * 10) / 10,\r\n        mostPostponedCategory\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting postpone stats:', error);\r\n      return {\r\n        totalPostponedTasks: 0,\r\n        averagePostponeCount: 0,\r\n        mostPostponedCategory: 'work'\r\n      };\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM;IACX,SAAS;IACQ,sBAAsB;QACrC,KAAK;QACL,QAAQ;QACR,MAAM;QACN,UAAU;IACZ,EAAE;IAEF;;GAEC,GACD,MAAM,sBAAsB,MAAc,EAAiC;QACzE,IAAI;YACF,YAAY;YACZ,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;YAEpD,OAAO,eAAe,GAAG,CAAC,CAAA;gBACxB,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;gBAChD,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC;gBAC9C,MAAM,mBAAmB,IAAI,CAAC,kBAAkB,CAAC,KAAK,SAAS;gBAE/D,OAAO;oBACL;oBACA,eAAe,KAAK,aAAa;oBACjC;oBACA;oBACA;oBACA,aAAa,iBAAiB;gBAChC;YACF,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,GAAG,aAAa;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAU,EAA0C;QAChF,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG;QACpC,MAAM,oBAAoB,IAAI,CAAC,kBAAkB,CAAC;QAClD,MAAM,mBAAmB,IAAI,CAAC,kBAAkB,CAAC,KAAK,SAAS;QAE/D,qBAAqB;QACrB,IAAI,eAAe;QAEnB,SAAS;QACT,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,gBAAgB;aACnE,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,gBAAgB;aACpE,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,gBAAgB;aACtE,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,gBAAgB;QAExE,SAAS;QACT,IAAI,oBAAoB,GAAG,gBAAgB,GAAG,MAAM;aAC/C,IAAI,oBAAoB,CAAC,GAAG,gBAAgB,GAAG,QAAQ;aACvD,IAAI,oBAAoB,CAAC,GAAG,gBAAgB,GAAG,QAAQ;QAE5D,mBAAmB;QACnB,IAAI,mBAAmB,IAAI,gBAAgB,GAAG,OAAO;aAChD,IAAI,mBAAmB,GAAG,gBAAgB,GAAG,OAAO;QAEzD,YAAY;QACZ,IAAI,KAAK,UAAU,IAAI,GAAG,gBAAgB;QAC1C,IAAI,KAAK,OAAO,IAAI,GAAG,gBAAgB;QAEvC,aAAa;QACb,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,GAAG,OAAO;QAC9B,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAU,EAAU;QAChD,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;QAChD,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG;QAEvD,cAAc;QACd,MAAM,kBAAkB;YACtB,UAAU;YACV,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QAEA,IAAI,aAAa,eAAe,CAAC,aAAa;QAE9C,cAAc;QACd,IAAI,iBAAiB,GAAG;YACtB,cAAc;QAChB,OAAO,IAAI,iBAAiB,GAAG;YAC7B,cAAc;QAChB;QAEA,YAAY;QACZ,IAAI,oBAAoB,KAAK;YAC3B,cAAc;QAChB;QAEA,UAAU;QACV,IAAI,aAAa,QAAQ;YACvB,cAAc;QAChB,OAAO,IAAI,aAAa,eAAe;YACrC,cAAc;QAChB,OAAO,IAAI,aAAa,iBAAiB;YACvC,cAAc;QAChB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,kBAAkB,MAAc,EAAmB;QAC/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,kBAAkB,GACrB,EAAE,CAAC,UAAU;YAAC;YAAW;SAAY,EACrC,KAAK,CAAC,kBAAkB;YAAE,WAAW;QAAM;QAE9C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,EAAE;QACX;QAEA,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,IAAI,KAAK,EAAE;gBACX,QAAQ,KAAK,OAAO;gBACpB,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU;gBAC3B,SAAS,KAAK,OAAO;gBACrB,UAAU,IAAI,KAAK,KAAK,QAAQ;gBAChC,mBAAmB,KAAK,kBAAkB;gBAC1C,QAAQ,KAAK,MAAM;gBACnB,eAAe,KAAK,cAAc;gBAClC,WAAW,IAAI,KAAK,KAAK,UAAU;gBACnC,WAAW,IAAI,KAAK,KAAK,UAAU;YACrC,CAAC;IACH;IAEA;;GAEC,GACD,AAAQ,mBAAmB,IAAU,EAAU;QAC7C,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,OAAO,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD;IAEA;;GAEC,GACD,MAAM,aAAa,MAAc,EAAE,MAAe,EAAiB;QACjE,IAAI;YACF,SAAS;YACT,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACrD,IAAI,CAAC,SACL,MAAM,CAAC,kBACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,YAAY,MAAM;YAEtB,SAAS;YACT,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,gBAAgB,CAAC,KAAK,cAAc,IAAI,CAAC,IAAI;gBAC7C,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,aAAa,MAAM;QAEvB,mBAAmB;QACnB,kBAAkB;QAEpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,wBAAwB,MAAc,EAAiB;QAC3D,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,gBAAgB;gBAChB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,MAAc,EAIlC;QACD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,4BACP,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,kBAAkB;YAExB,IAAI,OAAO,MAAM;YAEjB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,OAAO;oBACL,qBAAqB;oBACrB,sBAAsB;oBACtB,uBAAuB;gBACzB;YACF;YAEA,MAAM,sBAAsB,KAAK,MAAM;YACvC,MAAM,uBAAuB,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,cAAc,EAAE,KAAK;YAExF,aAAa;YACb,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAC,KAAK;gBACtC,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,cAAc;gBACpE,OAAO;YACT,GAAG,CAAC;YAEJ,MAAM,wBAAwB,OAAO,IAAI,CAAC,eAAe,MAAM,CAAC,CAAC,GAAG,IAClE,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,IAAI;YAG5C,OAAO;gBACL;gBACA,sBAAsB,KAAK,KAAK,CAAC,uBAAuB,MAAM;gBAC9D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBACL,qBAAqB;gBACrB,sBAAsB;gBACtB,uBAAuB;YACzB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/store/useTaskStore.ts"], "sourcesContent": ["import { create } from 'zustand';\r\nimport { Task, DailySchedule, BalanceAnalysis, PostponedTaskAlert } from '@/types';\r\nimport { supabase } from '@/lib/supabase';\r\nimport { PlanningAlgorithm } from '@/lib/algorithms/planning';\r\nimport { BalanceAlgorithm } from '@/lib/algorithms/balance';\r\nimport { FixAlgorithm } from '@/lib/algorithms/fix';\r\n\r\ninterface TaskState {\r\n  // State\r\n  tasks: Task[];\r\n  dailySchedule: DailySchedule | null;\r\n  balanceAnalysis: BalanceAnalysis | null;\r\n  postponedAlerts: PostponedTaskAlert[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  \r\n  // Algorithms\r\n  planningAlgorithm: PlanningAlgorithm;\r\n  balanceAlgorithm: BalanceAlgorithm;\r\n  fixAlgorithm: FixAlgorithm;\r\n  \r\n  // Actions\r\n  fetchTasks: (userId: string) => Promise<void>;\r\n  createTask: (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;\r\n  updateTask: (id: string, updates: Partial<Task>) => Promise<void>;\r\n  deleteTask: (id: string) => Promise<void>;\r\n  completeTask: (id: string, actualDuration?: number, satisfaction?: number) => Promise<void>;\r\n  postponeTask: (id: string, reason?: string) => Promise<void>;\r\n  \r\n  // Algorithm actions\r\n  generateDailySchedule: (userId: string) => Promise<void>;\r\n  analyzeBalance: (userId: string) => Promise<void>;\r\n  analyzePostponedTasks: (userId: string) => Promise<void>;\r\n  \r\n  // Utility actions\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const useTaskStore = create<TaskState>((set, get) => ({\r\n  // Initial state\r\n  tasks: [],\r\n  dailySchedule: null,\r\n  balanceAnalysis: null,\r\n  postponedAlerts: [],\r\n  loading: false,\r\n  error: null,\r\n  \r\n  // Algorithm instances\r\n  planningAlgorithm: new PlanningAlgorithm(),\r\n  balanceAlgorithm: new BalanceAlgorithm(),\r\n  fixAlgorithm: new FixAlgorithm(),\r\n  \r\n  // Fetch tasks\r\n  fetchTasks: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { data, error } = await supabase\r\n        .from('tasks')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n        .order('created_at', { ascending: false });\r\n      \r\n      if (error) throw error;\r\n      \r\n      const tasks: Task[] = data.map(task => ({\r\n        id: task.id,\r\n        userId: task.user_id,\r\n        title: task.title,\r\n        description: task.description,\r\n        category: task.category,\r\n        importance: task.importance,\r\n        urgency: task.urgency,\r\n        deadline: new Date(task.deadline),\r\n        estimatedDuration: task.estimated_duration,\r\n        status: task.status,\r\n        postponeCount: task.postpone_count,\r\n        createdAt: new Date(task.created_at),\r\n        updatedAt: new Date(task.updated_at)\r\n      }));\r\n      \r\n      set({ tasks, loading: false });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Create task\r\n  createTask: async (taskData) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { data, error } = await supabase\r\n        .from('tasks')\r\n        .insert({\r\n          user_id: taskData.userId,\r\n          title: taskData.title,\r\n          description: taskData.description,\r\n          category: taskData.category,\r\n          importance: taskData.importance,\r\n          urgency: taskData.urgency,\r\n          deadline: taskData.deadline.toISOString(),\r\n          estimated_duration: taskData.estimatedDuration,\r\n          status: taskData.status,\r\n          postpone_count: taskData.postponeCount\r\n        })\r\n        .select()\r\n        .single();\r\n      \r\n      if (error) throw error;\r\n      \r\n      const newTask: Task = {\r\n        id: data.id,\r\n        userId: data.user_id,\r\n        title: data.title,\r\n        description: data.description,\r\n        category: data.category,\r\n        importance: data.importance,\r\n        urgency: data.urgency,\r\n        deadline: new Date(data.deadline),\r\n        estimatedDuration: data.estimated_duration,\r\n        status: data.status,\r\n        postponeCount: data.postpone_count,\r\n        createdAt: new Date(data.created_at),\r\n        updatedAt: new Date(data.updated_at)\r\n      };\r\n      \r\n      set(state => ({\r\n        tasks: [newTask, ...state.tasks],\r\n        loading: false\r\n      }));\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create task';\r\n      set({ error: errorMessage, loading: false });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Update task\r\n  updateTask: async (id: string, updates: Partial<Task>) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const updateData: any = {};\r\n      if (updates.title) updateData.title = updates.title;\r\n      if (updates.description !== undefined) updateData.description = updates.description;\r\n      if (updates.category) updateData.category = updates.category;\r\n      if (updates.importance) updateData.importance = updates.importance;\r\n      if (updates.urgency) updateData.urgency = updates.urgency;\r\n      if (updates.deadline) updateData.deadline = updates.deadline.toISOString();\r\n      if (updates.estimatedDuration) updateData.estimated_duration = updates.estimatedDuration;\r\n      if (updates.status) updateData.status = updates.status;\r\n      if (updates.postponeCount !== undefined) updateData.postpone_count = updates.postponeCount;\r\n      \r\n      updateData.updated_at = new Date().toISOString();\r\n      \r\n      const { error } = await supabase\r\n        .from('tasks')\r\n        .update(updateData)\r\n        .eq('id', id);\r\n      \r\n      if (error) throw error;\r\n      \r\n      set(state => ({\r\n        tasks: state.tasks.map(task => \r\n          task.id === id ? { ...task, ...updates, updatedAt: new Date() } : task\r\n        ),\r\n        loading: false\r\n      }));\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to update task';\r\n      set({ error: errorMessage, loading: false });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Delete task\r\n  deleteTask: async (id: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { error } = await supabase\r\n        .from('tasks')\r\n        .delete()\r\n        .eq('id', id);\r\n      \r\n      if (error) throw error;\r\n      \r\n      set(state => ({\r\n        tasks: state.tasks.filter(task => task.id !== id),\r\n        loading: false\r\n      }));\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to delete task';\r\n      set({ error: errorMessage, loading: false });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Complete task\r\n  completeTask: async (id: string, actualDuration?: number, satisfaction?: number) => {\r\n    try {\r\n      const { updateTask, balanceAlgorithm } = get();\r\n      const task = get().tasks.find(t => t.id === id);\r\n      \r\n      if (!task) throw new Error('Task not found');\r\n      \r\n      // Update task status\r\n      await updateTask(id, { status: 'completed' });\r\n      \r\n      // Record completion\r\n      if (actualDuration && satisfaction) {\r\n        await supabase\r\n          .from('task_completions')\r\n          .insert({\r\n            task_id: id,\r\n            actual_duration: actualDuration,\r\n            satisfaction_score: satisfaction\r\n          });\r\n      }\r\n      \r\n      // Update daily stats\r\n      await balanceAlgorithm.updateTodayStats(\r\n        task.userId, \r\n        task.category, \r\n        actualDuration || task.estimatedDuration\r\n      );\r\n      \r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';\r\n      set({ error: errorMessage });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Postpone task\r\n  postponeTask: async (id: string, reason?: string) => {\r\n    try {\r\n      const { fixAlgorithm } = get();\r\n      await fixAlgorithm.postponeTask(id, reason);\r\n      \r\n      // Refresh tasks\r\n      const task = get().tasks.find(t => t.id === id);\r\n      if (task) {\r\n        await get().fetchTasks(task.userId);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to postpone task';\r\n      set({ error: errorMessage });\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Generate daily schedule\r\n  generateDailySchedule: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n\r\n      const { tasks, planningAlgorithm } = get();\r\n      const userTasks = tasks.filter(task => task.userId === userId);\r\n\r\n      // 获取用户时间配置\r\n      let userTimeConfig = null;\r\n      try {\r\n        const { data: userProfile } = await supabase\r\n          .from('user_profiles')\r\n          .select('time_config')\r\n          .eq('id', userId)\r\n          .single();\r\n\r\n        userTimeConfig = userProfile?.time_config;\r\n      } catch (error) {\r\n        console.log('No user time config found, using defaults');\r\n      }\r\n\r\n      const schedule = planningAlgorithm.generateDailySchedule(userTasks, userTimeConfig);\r\n\r\n      set({ dailySchedule: schedule, loading: false });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to generate schedule';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Analyze balance\r\n  analyzeBalance: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { balanceAlgorithm } = get();\r\n      const analysis = await balanceAlgorithm.analyzeWeeklyBalance(userId);\r\n      \r\n      set({ balanceAnalysis: analysis, loading: false });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze balance';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Analyze postponed tasks\r\n  analyzePostponedTasks: async (userId: string) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      \r\n      const { fixAlgorithm } = get();\r\n      const alerts = await fixAlgorithm.analyzePostponedTasks(userId);\r\n      \r\n      set({ postponedAlerts: alerts, loading: false });\r\n    } catch (error) {\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to analyze postponed tasks';\r\n      set({ error: errorMessage, loading: false });\r\n    }\r\n  },\r\n  \r\n  // Utility actions\r\n  setLoading: (loading: boolean) => set({ loading }),\r\n  setError: (error: string | null) => set({ error }),\r\n  clearError: () => set({ error: null })\r\n}));\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AACA;AACA;;;;;;AAmCO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,gBAAgB;QAChB,OAAO,EAAE;QACT,eAAe;QACf,iBAAiB;QACjB,iBAAiB,EAAE;QACnB,SAAS;QACT,OAAO;QAEP,sBAAsB;QACtB,mBAAmB,IAAI,uIAAA,CAAA,oBAAiB;QACxC,kBAAkB,IAAI,sIAAA,CAAA,mBAAgB;QACtC,cAAc,IAAI,kIAAA,CAAA,eAAY;QAE9B,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO,MAAM;gBAEjB,MAAM,QAAgB,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACtC,IAAI,KAAK,EAAE;wBACX,QAAQ,KAAK,OAAO;wBACpB,OAAO,KAAK,KAAK;wBACjB,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,SAAS,KAAK,OAAO;wBACrB,UAAU,IAAI,KAAK,KAAK,QAAQ;wBAChC,mBAAmB,KAAK,kBAAkB;wBAC1C,QAAQ,KAAK,MAAM;wBACnB,eAAe,KAAK,cAAc;wBAClC,WAAW,IAAI,KAAK,KAAK,UAAU;wBACnC,WAAW,IAAI,KAAK,KAAK,UAAU;oBACrC,CAAC;gBAED,IAAI;oBAAE;oBAAO,SAAS;gBAAM;YAC9B,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;oBACN,SAAS,SAAS,MAAM;oBACxB,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,UAAU;oBAC/B,SAAS,SAAS,OAAO;oBACzB,UAAU,SAAS,QAAQ,CAAC,WAAW;oBACvC,oBAAoB,SAAS,iBAAiB;oBAC9C,QAAQ,SAAS,MAAM;oBACvB,gBAAgB,SAAS,aAAa;gBACxC,GACC,MAAM,GACN,MAAM;gBAET,IAAI,OAAO,MAAM;gBAEjB,MAAM,UAAgB;oBACpB,IAAI,KAAK,EAAE;oBACX,QAAQ,KAAK,OAAO;oBACpB,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;oBAC7B,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,SAAS,KAAK,OAAO;oBACrB,UAAU,IAAI,KAAK,KAAK,QAAQ;oBAChC,mBAAmB,KAAK,kBAAkB;oBAC1C,QAAQ,KAAK,MAAM;oBACnB,eAAe,KAAK,cAAc;oBAClC,WAAW,IAAI,KAAK,KAAK,UAAU;oBACnC,WAAW,IAAI,KAAK,KAAK,UAAU;gBACrC;gBAEA,IAAI,CAAA,QAAS,CAAC;wBACZ,OAAO;4BAAC;+BAAY,MAAM,KAAK;yBAAC;wBAChC,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,cAAc;QACd,YAAY,OAAO,IAAY;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,aAAkB,CAAC;gBACzB,IAAI,QAAQ,KAAK,EAAE,WAAW,KAAK,GAAG,QAAQ,KAAK;gBACnD,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;gBACnF,IAAI,QAAQ,QAAQ,EAAE,WAAW,QAAQ,GAAG,QAAQ,QAAQ;gBAC5D,IAAI,QAAQ,UAAU,EAAE,WAAW,UAAU,GAAG,QAAQ,UAAU;gBAClE,IAAI,QAAQ,OAAO,EAAE,WAAW,OAAO,GAAG,QAAQ,OAAO;gBACzD,IAAI,QAAQ,QAAQ,EAAE,WAAW,QAAQ,GAAG,QAAQ,QAAQ,CAAC,WAAW;gBACxE,IAAI,QAAQ,iBAAiB,EAAE,WAAW,kBAAkB,GAAG,QAAQ,iBAAiB;gBACxF,IAAI,QAAQ,MAAM,EAAE,WAAW,MAAM,GAAG,QAAQ,MAAM;gBACtD,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,cAAc,GAAG,QAAQ,aAAa;gBAE1F,WAAW,UAAU,GAAG,IAAI,OAAO,WAAW;gBAE9C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO,MAAM;gBAEjB,IAAI,CAAA,QAAS,CAAC;wBACZ,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,EAAE,KAAK,KAAK;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;wBAEpE,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,cAAc;QACd,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;gBAEZ,IAAI,OAAO,MAAM;gBAEjB,IAAI,CAAA,QAAS,CAAC;wBACZ,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;wBAC9C,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,MAAM;YACR;QACF;QAEA,gBAAgB;QAChB,cAAc,OAAO,IAAY,gBAAyB;YACxD,IAAI;gBACF,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,GAAG;gBACzC,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAE5C,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;gBAE3B,qBAAqB;gBACrB,MAAM,WAAW,IAAI;oBAAE,QAAQ;gBAAY;gBAE3C,oBAAoB;gBACpB,IAAI,kBAAkB,cAAc;oBAClC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,oBACL,MAAM,CAAC;wBACN,SAAS;wBACT,iBAAiB;wBACjB,oBAAoB;oBACtB;gBACJ;gBAEA,qBAAqB;gBACrB,MAAM,iBAAiB,gBAAgB,CACrC,KAAK,MAAM,EACX,KAAK,QAAQ,EACb,kBAAkB,KAAK,iBAAiB;YAG5C,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,gBAAgB;QAChB,cAAc,OAAO,IAAY;YAC/B,IAAI;gBACF,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,MAAM,aAAa,YAAY,CAAC,IAAI;gBAEpC,gBAAgB;gBAChB,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC5C,IAAI,MAAM;oBACR,MAAM,MAAM,UAAU,CAAC,KAAK,MAAM;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;gBAAa;gBAC1B,MAAM;YACR;QACF;QAEA,0BAA0B;QAC1B,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG;gBACrC,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;gBAEvD,WAAW;gBACX,IAAI,iBAAiB;gBACrB,IAAI;oBACF,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACzC,IAAI,CAAC,iBACL,MAAM,CAAC,eACP,EAAE,CAAC,MAAM,QACT,MAAM;oBAET,iBAAiB,aAAa;gBAChC,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC;gBACd;gBAEA,MAAM,WAAW,kBAAkB,qBAAqB,CAAC,WAAW;gBAEpE,IAAI;oBAAE,eAAe;oBAAU,SAAS;gBAAM;YAChD,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,kBAAkB;QAClB,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,gBAAgB,EAAE,GAAG;gBAC7B,MAAM,WAAW,MAAM,iBAAiB,oBAAoB,CAAC;gBAE7D,IAAI;oBAAE,iBAAiB;oBAAU,SAAS;gBAAM;YAClD,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,0BAA0B;QAC1B,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,EAAE,YAAY,EAAE,GAAG;gBACzB,MAAM,SAAS,MAAM,aAAa,qBAAqB,CAAC;gBAExD,IAAI;oBAAE,iBAAiB;oBAAQ,SAAS;gBAAM;YAChD,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;YAC5C;QACF;QAEA,kBAAkB;QAClB,YAAY,CAAC,UAAqB,IAAI;gBAAE;YAAQ;QAChD,UAAU,CAAC,QAAyB,IAAI;gBAAE;YAAM;QAChD,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC", "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/components/QuadrantSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface QuadrantSelectorProps {\n  importance: number;\n  urgency: number;\n  onChange: (importance: number, urgency: number) => void;\n}\n\nexport default function QuadrantSelector({ importance, urgency, onChange }: QuadrantSelectorProps) {\n  const [selectedQuadrant, setSelectedQuadrant] = useState<number | null>(null);\n\n  // 根据重要性和紧急性确定象限\n  const getQuadrant = (imp: number, urg: number): number => {\n    if (imp >= 4 && urg >= 4) return 1; // 重要且紧急\n    if (imp >= 4 && urg < 4) return 2;  // 重要不紧急\n    if (imp < 4 && urg >= 4) return 3;  // 不重要但紧急\n    return 4; // 不重要不紧急\n  };\n\n  const currentQuadrant = getQuadrant(importance, urgency);\n\n  // 象限配置\n  const quadrants = [\n    {\n      id: 1,\n      title: '重要且紧急',\n      description: '立即处理',\n      color: 'bg-red-100 border-red-300 hover:bg-red-200',\n      selectedColor: 'bg-red-200 border-red-500',\n      textColor: 'text-red-800',\n      importance: 5,\n      urgency: 5,\n      position: 'top-right'\n    },\n    {\n      id: 2,\n      title: '重要不紧急',\n      description: '计划安排',\n      color: 'bg-blue-100 border-blue-300 hover:bg-blue-200',\n      selectedColor: 'bg-blue-200 border-blue-500',\n      textColor: 'text-blue-800',\n      importance: 5,\n      urgency: 2,\n      position: 'top-left'\n    },\n    {\n      id: 3,\n      title: '不重要但紧急',\n      description: '委托处理',\n      color: 'bg-yellow-100 border-yellow-300 hover:bg-yellow-200',\n      selectedColor: 'bg-yellow-200 border-yellow-500',\n      textColor: 'text-yellow-800',\n      importance: 2,\n      urgency: 5,\n      position: 'bottom-right'\n    },\n    {\n      id: 4,\n      title: '不重要不紧急',\n      description: '减少或删除',\n      color: 'bg-gray-100 border-gray-300 hover:bg-gray-200',\n      selectedColor: 'bg-gray-200 border-gray-500',\n      textColor: 'text-gray-800',\n      importance: 2,\n      urgency: 2,\n      position: 'bottom-left'\n    }\n  ];\n\n  const handleQuadrantClick = (quadrant: typeof quadrants[0]) => {\n    setSelectedQuadrant(quadrant.id);\n    onChange(quadrant.importance, quadrant.urgency);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"text-sm font-medium text-gray-700 mb-2\">\n        任务优先级（点击象限选择）\n      </div>\n      \n      {/* 四象限网格 */}\n      <div className=\"relative w-full max-w-md mx-auto\">\n        {/* 坐标轴标签 */}\n        <div className=\"absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 font-medium\">\n          紧急程度 →\n        </div>\n        <div className=\"absolute -left-12 top-1/2 transform -translate-y-1/2 -rotate-90 text-xs text-gray-500 font-medium\">\n          重要程度 →\n        </div>\n        \n        {/* 象限网格 */}\n        <div className=\"grid grid-cols-2 gap-2 w-80 h-80\">\n          {/* 第二象限：重要不紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 2 ? quadrants[1].selectedColor : quadrants[1].color}\n              ${quadrants[1].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[1])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">重要</div>\n            <div className=\"font-semibold text-sm mb-2\">不紧急</div>\n            <div className=\"text-xs opacity-75\">计划安排</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 II</div>\n          </div>\n\n          {/* 第一象限：重要且紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 1 ? quadrants[0].selectedColor : quadrants[0].color}\n              ${quadrants[0].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[0])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">重要</div>\n            <div className=\"font-semibold text-sm mb-2\">紧急</div>\n            <div className=\"text-xs opacity-75\">立即处理</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 I</div>\n          </div>\n\n          {/* 第四象限：不重要不紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 4 ? quadrants[3].selectedColor : quadrants[3].color}\n              ${quadrants[3].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[3])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">不重要</div>\n            <div className=\"font-semibold text-sm mb-2\">不紧急</div>\n            <div className=\"text-xs opacity-75\">减少删除</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 IV</div>\n          </div>\n\n          {/* 第三象限：不重要但紧急 */}\n          <div\n            className={`\n              border-2 rounded-lg p-4 cursor-pointer transition-all duration-200\n              flex flex-col justify-center items-center text-center\n              ${currentQuadrant === 3 ? quadrants[2].selectedColor : quadrants[2].color}\n              ${quadrants[2].textColor}\n            `}\n            onClick={() => handleQuadrantClick(quadrants[2])}\n          >\n            <div className=\"font-semibold text-sm mb-1\">不重要</div>\n            <div className=\"font-semibold text-sm mb-2\">紧急</div>\n            <div className=\"text-xs opacity-75\">委托处理</div>\n            <div className=\"text-xs mt-1 font-bold\">象限 III</div>\n          </div>\n        </div>\n\n        {/* 当前选择显示 */}\n        <div className=\"mt-4 text-center\">\n          <div className=\"text-sm text-gray-600\">\n            当前选择：<span className=\"font-medium\">象限 {currentQuadrant}</span>\n          </div>\n          <div className=\"text-xs text-gray-500 mt-1\">\n            重要性: {importance}/5 | 紧急性: {urgency}/5\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUe,SAAS,iBAAiB,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAyB;;IAC/F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,gBAAgB;IAChB,MAAM,cAAc,CAAC,KAAa;QAChC,IAAI,OAAO,KAAK,OAAO,GAAG,OAAO,GAAG,QAAQ;QAC5C,IAAI,OAAO,KAAK,MAAM,GAAG,OAAO,GAAI,QAAQ;QAC5C,IAAI,MAAM,KAAK,OAAO,GAAG,OAAO,GAAI,SAAS;QAC7C,OAAO,GAAG,SAAS;IACrB;IAEA,MAAM,kBAAkB,YAAY,YAAY;IAEhD,OAAO;IACP,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,WAAW;YACX,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;KACD;IAED,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB,SAAS,EAAE;QAC/B,SAAS,SAAS,UAAU,EAAE,SAAS,OAAO;IAChD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAAyC;;;;;;0BAKxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCAAwF;;;;;;kCAGvG,6LAAC;wBAAI,WAAU;kCAAoG;;;;;;kCAKnH,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,6LAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAI1C,6LAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,6LAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAI1C,6LAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,6LAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAI1C,6LAAC;gCACC,WAAW,CAAC;;;cAGV,EAAE,oBAAoB,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;cAC1E,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;YAC3B,CAAC;gCACD,SAAS,IAAM,oBAAoB,SAAS,CAAC,EAAE;;kDAE/C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,6LAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAK5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAwB;kDAChC,6LAAC;wCAAK,WAAU;;4CAAc;4CAAI;;;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;oCAA6B;oCACpC;oCAAW;oCAAW;oCAAQ;;;;;;;;;;;;;;;;;;;;;;;;;AAMhD;GAjKwB;KAAA", "debugId": null}}, {"offset": {"line": 1383, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/src/app/tasks/new/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuthStore } from '@/store/useAuthStore';\r\nimport { useTaskStore } from '@/store/useTaskStore';\r\nimport { ArrowLeft, Save } from 'lucide-react';\r\nimport QuadrantSelector from '@/components/QuadrantSelector';\r\n\r\nexport default function NewTask() {\r\n  const router = useRouter();\r\n  const { user } = useAuthStore();\r\n  const { createTask, loading } = useTaskStore();\r\n  \r\n  const [formData, setFormData] = useState({\r\n    title: '',\r\n    category: 'work' as 'work' | 'improvement' | 'entertainment',\r\n    importance: 3,\r\n    urgency: 3,\r\n    deadline: '',\r\n    estimatedDuration: 1 // 改为小时单位\r\n  });\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!user) {\r\n      router.push('/auth/signin');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await createTask({\r\n        userId: user.id,\r\n        title: formData.title,\r\n        description: '', // 移除description字段，设为空字符串\r\n        category: formData.category,\r\n        importance: formData.importance as 1 | 2 | 3 | 4 | 5,\r\n        urgency: formData.urgency as 1 | 2 | 3 | 4 | 5,\r\n        deadline: new Date(formData.deadline),\r\n        estimatedDuration: formData.estimatedDuration * 60, // 转换为分钟\r\n        status: 'pending',\r\n        postponeCount: 0\r\n      });\r\n      \r\n      router.push('/dashboard');\r\n    } catch (error) {\r\n      console.error('Failed to create task:', error);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value, type } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'number' ? parseInt(value) : value\r\n    }));\r\n  };\r\n\r\n  // 处理四象限选择器的变化\r\n  const handleQuadrantChange = (importance: number, urgency: number) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      importance,\r\n      urgency\r\n    }));\r\n  };\r\n\r\n  // 设置默认截止时间为明天\r\n  const getDefaultDeadline = () => {\r\n    const tomorrow = new Date();\r\n    tomorrow.setDate(tomorrow.getDate() + 1);\r\n    tomorrow.setHours(18, 0, 0, 0); // 默认下午6点\r\n    return tomorrow.toISOString().slice(0, 16); // YYYY-MM-DDTHH:MM format\r\n  };\r\n\r\n  if (!formData.deadline) {\r\n    setFormData(prev => ({ ...prev, deadline: getDefaultDeadline() }));\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Header */}\r\n      <header className=\"bg-white shadow-sm border-b\">\r\n        <div className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex items-center h-16\">\r\n            <button\r\n              onClick={() => router.back()}\r\n              className=\"flex items-center text-gray-600 hover:text-gray-900 mr-4\"\r\n            >\r\n              <ArrowLeft className=\"h-5 w-5 mr-1\" />\r\n              返回\r\n            </button>\r\n            <h1 className=\"text-xl font-semibold text-gray-900\">创建新任务</h1>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main Content */}\r\n      <main className=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        <div className=\"bg-white rounded-lg shadow p-6\">\r\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n            {/* 任务标题 */}\r\n            <div>\r\n              <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                任务标题 *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"title\"\r\n                name=\"title\"\r\n                required\r\n                value={formData.title}\r\n                onChange={handleChange}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                placeholder=\"请输入任务标题\"\r\n              />\r\n            </div>\r\n\r\n            {/* 四象限优先级选择器 */}\r\n            <div>\r\n              <QuadrantSelector\r\n                importance={formData.importance}\r\n                urgency={formData.urgency}\r\n                onChange={handleQuadrantChange}\r\n              />\r\n            </div>\r\n\r\n            {/* 任务分类 */}\r\n            <div>\r\n              <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                任务分类 *\r\n              </label>\r\n              <select\r\n                id=\"category\"\r\n                name=\"category\"\r\n                required\r\n                value={formData.category}\r\n                onChange={handleChange}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n              >\r\n                <option value=\"work\">工作</option>\r\n                <option value=\"improvement\">提升</option>\r\n                <option value=\"entertainment\">娱乐</option>\r\n              </select>\r\n              <p className=\"mt-1 text-sm text-gray-500\">\r\n                工作：日常工作任务 | 提升：学习和自我提升 | 娱乐：休闲和放松\r\n              </p>\r\n            </div>\r\n\r\n\r\n\r\n            {/* 截止时间和预估时长 */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div>\r\n                <label htmlFor=\"deadline\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  截止时间 *\r\n                </label>\r\n                <input\r\n                  type=\"datetime-local\"\r\n                  id=\"deadline\"\r\n                  name=\"deadline\"\r\n                  required\r\n                  value={formData.deadline}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label htmlFor=\"estimatedDuration\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  预估时长（小时）*\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  id=\"estimatedDuration\"\r\n                  name=\"estimatedDuration\"\r\n                  required\r\n                  min=\"0.25\"\r\n                  max=\"8\"\r\n                  step=\"0.25\"\r\n                  value={formData.estimatedDuration}\r\n                  onChange={handleChange}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\r\n                />\r\n                <p className=\"mt-1 text-sm text-gray-500\">建议：0.25-8小时（15分钟-8小时）</p>\r\n              </div>\r\n            </div>\r\n\r\n\r\n\r\n            {/* 提交按钮 */}\r\n            <div className=\"flex justify-end space-x-3\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => router.back()}\r\n                className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n              >\r\n                取消\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className=\"flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? (\r\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                ) : (\r\n                  <Save className=\"h-4 w-4 mr-2\" />\r\n                )}\r\n                创建任务\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAE3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,YAAY;QACZ,SAAS;QACT,UAAU;QACV,mBAAmB,EAAE,SAAS;IAChC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,WAAW;gBACf,QAAQ,KAAK,EAAE;gBACf,OAAO,SAAS,KAAK;gBACrB,aAAa;gBACb,UAAU,SAAS,QAAQ;gBAC3B,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,UAAU,IAAI,KAAK,SAAS,QAAQ;gBACpC,mBAAmB,SAAS,iBAAiB,GAAG;gBAChD,QAAQ;gBACR,eAAe;YACjB;YAEA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,WAAW,SAAS,SAAS;YAChD,CAAC;IACH;IAEA,cAAc;IACd,MAAM,uBAAuB,CAAC,YAAoB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP;gBACA;YACF,CAAC;IACH;IAEA,cAAc;IACd,MAAM,qBAAqB;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QACtC,SAAS,QAAQ,CAAC,IAAI,GAAG,GAAG,IAAI,SAAS;QACzC,OAAO,SAAS,WAAW,GAAG,KAAK,CAAC,GAAG,KAAK,0BAA0B;IACxE;IAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAqB,CAAC;IAClE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI;gCAC1B,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;0BAM1D,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAA+C;;;;;;kDAGhF,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,KAAK;wCACrB,UAAU;wCACV,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAKhB,6LAAC;0CACC,cAAA,6LAAC,yIAAA,CAAA,UAAgB;oCACf,YAAY,SAAS,UAAU;oCAC/B,SAAS,SAAS,OAAO;oCACzB,UAAU;;;;;;;;;;;0CAKd,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAA+C;;;;;;kDAGnF,6LAAC;wCACC,IAAG;wCACH,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,6LAAC;gDAAO,OAAM;0DAAgB;;;;;;;;;;;;kDAEhC,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAQ5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA+C;;;;;;0DAGnF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAoB,WAAU;0DAA+C;;;;;;0DAG5F,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,QAAQ;gDACR,KAAI;gDACJ,KAAI;gDACJ,MAAK;gDACL,OAAO,SAAS,iBAAiB;gDACjC,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAO9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;;4CAET,wBACC,6LAAC;gDAAI,WAAU;;;;;qEAEf,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAjNwB;;QACP,qIAAA,CAAA,YAAS;QACP,+HAAA,CAAA,eAAY;QACG,+HAAA,CAAA,eAAY;;;KAHtB", "debugId": null}}, {"offset": {"line": 1802, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/augment-projects/TimeManager/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1809, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1845, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1872, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uKAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,0KAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKACjF,gBAAA,4JAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,kLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,kLAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1944, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1990, "column": 0}, "map": {"version": 3, "file": "save.js", "sources": ["file:///D:/augment-projects/TimeManager/node_modules/lucide-react/src/icons/save.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('save', __iconNode);\n\nexport default Save;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}