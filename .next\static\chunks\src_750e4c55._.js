(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/shared/constants/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 应用常量定义
 */ // ============================================================================
// 任务相关常量
// ============================================================================
__turbopack_context__.s({
    "ALGORITHM_CONSTANTS": (()=>ALGORITHM_CONSTANTS),
    "ALL_DAYS": (()=>ALL_DAYS),
    "DAY_NAMES_CN": (()=>DAY_NAMES_CN),
    "DEFAULT_USER_CONFIG": (()=>DEFAULT_USER_CONFIG),
    "ERROR_CODES": (()=>ERROR_CODES),
    "PRIORITY_LEVELS": (()=>PRIORITY_LEVELS),
    "QUADRANTS": (()=>QUADRANTS),
    "TASK_CATEGORIES": (()=>TASK_CATEGORIES),
    "TASK_STATUSES": (()=>TASK_STATUSES),
    "TIME_CONSTANTS": (()=>TIME_CONSTANTS),
    "UI_CONSTANTS": (()=>UI_CONSTANTS),
    "WORK_DAYS": (()=>WORK_DAYS)
});
const TASK_CATEGORIES = {
    WORK: 'work',
    IMPROVEMENT: 'improvement',
    ENTERTAINMENT: 'entertainment'
};
const TASK_STATUSES = {
    PENDING: 'pending',
    IN_PROGRESS: 'in-progress',
    COMPLETED: 'completed',
    POSTPONED: 'postponed'
};
const PRIORITY_LEVELS = {
    VERY_LOW: 1,
    LOW: 2,
    MEDIUM: 3,
    HIGH: 4,
    VERY_HIGH: 5
};
const QUADRANTS = {
    URGENT_IMPORTANT: 1,
    IMPORTANT_NOT_URGENT: 2,
    URGENT_NOT_IMPORTANT: 3,
    NOT_URGENT_NOT_IMPORTANT: 4 // 不紧急不重要
};
const TIME_CONSTANTS = {
    MINUTES_PER_HOUR: 60,
    HOURS_PER_DAY: 24,
    DAYS_PER_WEEK: 7,
    DEFAULT_BREAK_DURATION: 15,
    DEFAULT_TASK_DURATION: 60,
    MIN_TASK_DURATION: 15,
    MAX_TASK_DURATION: 480 // 分钟 (8小时)
};
const WORK_DAYS = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday'
];
const ALL_DAYS = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday'
];
const DAY_NAMES_CN = {
    monday: '周一',
    tuesday: '周二',
    wednesday: '周三',
    thursday: '周四',
    friday: '周五',
    saturday: '周六',
    sunday: '周日'
};
const DEFAULT_USER_CONFIG = {
    workStart: '09:00',
    workEnd: '18:00',
    workDays: WORK_DAYS,
    sleepStart: '23:00',
    sleepEnd: '07:00',
    fixedSlots: [
        {
            start: '07:00',
            end: '08:00',
            type: 'personal',
            label: '晨间例行'
        },
        {
            start: '12:00',
            end: '13:00',
            type: 'meal',
            label: '午餐时间'
        },
        {
            start: '18:30',
            end: '19:30',
            type: 'meal',
            label: '晚餐时间'
        }
    ],
    commuteToWork: 30,
    commuteFromWork: 30,
    preferredWorkHours: 'morning',
    maxContinuousWork: 120,
    breakInterval: 25,
    categoryPreferences: {
        work: {
            preferredTimes: [
                '09:00-12:00',
                '14:00-18:00'
            ],
            maxDaily: 480
        },
        improvement: {
            preferredTimes: [
                '07:00-09:00',
                '19:00-21:00'
            ],
            maxDaily: 120
        },
        entertainment: {
            preferredTimes: [
                '20:00-22:00'
            ],
            maxDaily: 180
        }
    }
};
const ALGORITHM_CONSTANTS = {
    // 四象限权重
    QUADRANT_WEIGHTS: {
        [QUADRANTS.URGENT_IMPORTANT]: 1.0,
        [QUADRANTS.IMPORTANT_NOT_URGENT]: 0.8,
        [QUADRANTS.URGENT_NOT_IMPORTANT]: 0.6,
        [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: 0.4
    },
    // 推迟惩罚系数
    POSTPONE_PENALTY: 0.1,
    // 截止日期权重
    DEADLINE_WEIGHT: 0.3,
    // 分类权重
    CATEGORY_WEIGHTS: {
        [TASK_CATEGORIES.WORK]: 1.0,
        [TASK_CATEGORIES.IMPROVEMENT]: 0.8,
        [TASK_CATEGORIES.ENTERTAINMENT]: 0.6
    }
};
const UI_CONSTANTS = {
    // 颜色主题
    COLORS: {
        PRIMARY: '#4F46E5',
        SUCCESS: '#10B981',
        WARNING: '#F59E0B',
        ERROR: '#EF4444',
        INFO: '#3B82F6'
    },
    // 象限颜色
    QUADRANT_COLORS: {
        [QUADRANTS.URGENT_IMPORTANT]: {
            bg: 'bg-red-100',
            border: 'border-red-300',
            text: 'text-red-800',
            selectedBg: 'bg-red-200',
            selectedBorder: 'border-red-500'
        },
        [QUADRANTS.IMPORTANT_NOT_URGENT]: {
            bg: 'bg-blue-100',
            border: 'border-blue-300',
            text: 'text-blue-800',
            selectedBg: 'bg-blue-200',
            selectedBorder: 'border-blue-500'
        },
        [QUADRANTS.URGENT_NOT_IMPORTANT]: {
            bg: 'bg-yellow-100',
            border: 'border-yellow-300',
            text: 'text-yellow-800',
            selectedBg: 'bg-yellow-200',
            selectedBorder: 'border-yellow-500'
        },
        [QUADRANTS.NOT_URGENT_NOT_IMPORTANT]: {
            bg: 'bg-gray-100',
            border: 'border-gray-300',
            text: 'text-gray-800',
            selectedBg: 'bg-gray-200',
            selectedBorder: 'border-gray-500'
        }
    },
    // 分页
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100
};
const ERROR_CODES = {
    // 通用错误
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    // 认证错误
    UNAUTHORIZED: 'UNAUTHORIZED',
    FORBIDDEN: 'FORBIDDEN',
    // 任务相关错误
    TASK_NOT_FOUND: 'TASK_NOT_FOUND',
    TASK_CREATION_FAILED: 'TASK_CREATION_FAILED',
    TASK_UPDATE_FAILED: 'TASK_UPDATE_FAILED',
    // 用户配置错误
    USER_CONFIG_NOT_FOUND: 'USER_CONFIG_NOT_FOUND',
    USER_CONFIG_INVALID: 'USER_CONFIG_INVALID',
    // 算法错误
    ALGORITHM_EXECUTION_FAILED: 'ALGORITHM_EXECUTION_FAILED',
    SCHEDULE_GENERATION_FAILED: 'SCHEDULE_GENERATION_FAILED'
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/shared/utils/dateUtils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 日期和时间相关的工具函数
 */ __turbopack_context__.s({
    "createDateTime": (()=>createDateTime),
    "formatDate": (()=>formatDate),
    "formatDateTime": (()=>formatDateTime),
    "formatDuration": (()=>formatDuration),
    "formatTime": (()=>formatTime),
    "getDaysDifference": (()=>getDaysDifference),
    "getDefaultDeadline": (()=>getDefaultDeadline),
    "getEndOfDay": (()=>getEndOfDay),
    "getEndOfWeek": (()=>getEndOfWeek),
    "getMinutesBetween": (()=>getMinutesBetween),
    "getNextWorkday": (()=>getNextWorkday),
    "getRelativeTime": (()=>getRelativeTime),
    "getStartOfDay": (()=>getStartOfDay),
    "getStartOfWeek": (()=>getStartOfWeek),
    "getTomorrow": (()=>getTomorrow),
    "isSameDay": (()=>isSameDay),
    "isTimeInRange": (()=>isTimeInRange),
    "isTimeRangeOverlap": (()=>isTimeRangeOverlap),
    "isToday": (()=>isToday),
    "isTomorrow": (()=>isTomorrow),
    "isWorkday": (()=>isWorkday),
    "parseTimeToday": (()=>parseTimeToday)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-client] (ecmascript)");
;
function formatDate(date) {
    return date.toISOString().split('T')[0];
}
function formatTime(date) {
    return date.toTimeString().slice(0, 5);
}
function formatDateTime(date) {
    return date.toISOString().slice(0, 16);
}
function formatDuration(minutes) {
    if (minutes < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TIME_CONSTANTS"].MINUTES_PER_HOUR) {
        return `${minutes}分钟`;
    }
    const hours = Math.floor(minutes / __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TIME_CONSTANTS"].MINUTES_PER_HOUR);
    const remainingMinutes = minutes % __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TIME_CONSTANTS"].MINUTES_PER_HOUR;
    if (remainingMinutes === 0) {
        return `${hours}小时`;
    }
    return `${hours}小时${remainingMinutes}分钟`;
}
function getStartOfDay(date = new Date()) {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0);
    return start;
}
function getEndOfDay(date = new Date()) {
    const end = new Date(date);
    end.setHours(23, 59, 59, 999);
    return end;
}
function getTomorrow(date = new Date()) {
    const tomorrow = new Date(date);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow;
}
function getStartOfWeek(date = new Date()) {
    const start = new Date(date);
    const day = start.getDay();
    const diff = start.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
    start.setDate(diff);
    return getStartOfDay(start);
}
function getEndOfWeek(date = new Date()) {
    const end = new Date(getStartOfWeek(date));
    end.setDate(end.getDate() + 6);
    return getEndOfDay(end);
}
function isSameDay(date1, date2) {
    return formatDate(date1) === formatDate(date2);
}
function isToday(date) {
    return isSameDay(date, new Date());
}
function isTomorrow(date) {
    return isSameDay(date, getTomorrow());
}
function getDaysDifference(date1, date2) {
    const timeDiff = date2.getTime() - date1.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
}
function parseTimeToday(timeStr, baseDate = new Date()) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const date = new Date(baseDate);
    date.setHours(hours, minutes, 0, 0);
    return date;
}
function createDateTime(date, timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const result = new Date(date);
    result.setHours(hours, minutes, 0, 0);
    return result;
}
function getDefaultDeadline() {
    const tomorrow = getTomorrow();
    tomorrow.setHours(18, 0, 0, 0);
    return tomorrow;
}
function isTimeInRange(time, startTime, endTime, baseDate = new Date()) {
    const start = parseTimeToday(startTime, baseDate);
    const end = parseTimeToday(endTime, baseDate);
    return time >= start && time <= end;
}
function isTimeRangeOverlap(start1, end1, start2, end2) {
    return start1 < end2 && end1 > start2;
}
function getMinutesBetween(start, end) {
    return Math.round((end.getTime() - start.getTime()) / (1000 * 60));
}
function isWorkday(date, workDays = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday'
]) {
    const dayNames = [
        'sunday',
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday'
    ];
    const dayName = dayNames[date.getDay()];
    return workDays.includes(dayName);
}
function getNextWorkday(date = new Date(), workDays = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday'
]) {
    let nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);
    while(!isWorkday(nextDay, workDays)){
        nextDay.setDate(nextDay.getDate() + 1);
    }
    return nextDay;
}
function getRelativeTime(date) {
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    if (diffDays === 0) {
        return '今天';
    } else if (diffDays === 1) {
        return '明天';
    } else if (diffDays === -1) {
        return '昨天';
    } else if (diffDays > 1 && diffDays <= 7) {
        return `${diffDays}天后`;
    } else if (diffDays < -1 && diffDays >= -7) {
        return `${Math.abs(diffDays)}天前`;
    } else {
        return formatDate(date);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/shared/utils/taskUtils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 任务相关的工具函数
 */ __turbopack_context__.s({
    "canStartTask": (()=>canStartTask),
    "classifyQuadrant": (()=>classifyQuadrant),
    "filterTasksByCategory": (()=>filterTasksByCategory),
    "filterTasksByQuadrant": (()=>filterTasksByQuadrant),
    "filterTasksByStatus": (()=>filterTasksByStatus),
    "getCategoryColor": (()=>getCategoryColor),
    "getCategoryIcon": (()=>getCategoryIcon),
    "getCategoryName": (()=>getCategoryName),
    "getPriorityColor": (()=>getPriorityColor),
    "getPriorityName": (()=>getPriorityName),
    "getQuadrantDescription": (()=>getQuadrantDescription),
    "getQuadrantTitle": (()=>getQuadrantTitle),
    "getStatusColor": (()=>getStatusColor),
    "getStatusName": (()=>getStatusName),
    "getTaskUrgencyDescription": (()=>getTaskUrgencyDescription),
    "getTodayTasks": (()=>getTodayTasks),
    "isTaskCompleted": (()=>isTaskCompleted),
    "isTaskDueSoon": (()=>isTaskDueSoon),
    "isTaskInProgress": (()=>isTaskInProgress),
    "isTaskOverdue": (()=>isTaskOverdue),
    "sortTasksByDeadline": (()=>sortTasksByDeadline),
    "sortTasksByPriority": (()=>sortTasksByPriority),
    "validateTask": (()=>validateTask)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-client] (ecmascript)");
;
function classifyQuadrant(importance, urgency) {
    if (importance >= 4 && urgency >= 4) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT;
    } else if (importance >= 4 && urgency < 4) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT;
    } else if (importance < 4 && urgency >= 4) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT;
    } else {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT;
    }
}
function getQuadrantDescription(quadrant) {
    const descriptions = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT]: '重要且紧急 - 立即执行',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT]: '重要不紧急 - 计划执行',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT]: '不重要但紧急 - 委托处理',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT]: '不重要不紧急 - 减少或删除'
    };
    return descriptions[quadrant];
}
function getQuadrantTitle(quadrant) {
    const titles = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT]: '象限 I',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT]: '象限 II',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT]: '象限 III',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT]: '象限 IV'
    };
    return titles[quadrant];
}
function getCategoryName(category) {
    const names = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK]: '工作',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].IMPROVEMENT]: '提升',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT]: '娱乐'
    };
    return names[category];
}
function getCategoryColor(category) {
    const colors = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK]: 'blue',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].IMPROVEMENT]: 'green',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT]: 'purple'
    };
    return colors[category];
}
function getCategoryIcon(category) {
    const icons = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK]: '💼',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].IMPROVEMENT]: '📚',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT]: '🎮'
    };
    return icons[category];
}
function getStatusName(status) {
    const names = {
        pending: '待处理',
        'in-progress': '进行中',
        completed: '已完成',
        postponed: '已推迟'
    };
    return names[status];
}
function getStatusColor(status) {
    const colors = {
        pending: 'gray',
        'in-progress': 'blue',
        completed: 'green',
        postponed: 'yellow'
    };
    return colors[status];
}
function canStartTask(task) {
    return task.status === 'pending' || task.status === 'postponed';
}
function isTaskCompleted(task) {
    return task.status === 'completed';
}
function isTaskInProgress(task) {
    return task.status === 'in-progress';
}
function getPriorityName(priority) {
    const names = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].VERY_LOW]: '很低',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].LOW]: '较低',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].MEDIUM]: '中等',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].HIGH]: '较高',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].VERY_HIGH]: '很高'
    };
    return names[priority];
}
function getPriorityColor(priority) {
    const colors = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].VERY_LOW]: 'gray',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].LOW]: 'blue',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].MEDIUM]: 'yellow',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].HIGH]: 'orange',
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIORITY_LEVELS"].VERY_HIGH]: 'red'
    };
    return colors[priority];
}
function filterTasksByCategory(tasks, category) {
    return tasks.filter((task)=>task.category === category);
}
function filterTasksByStatus(tasks, status) {
    return tasks.filter((task)=>task.status === status);
}
function filterTasksByQuadrant(tasks, quadrant) {
    return tasks.filter((task)=>classifyQuadrant(task.importance, task.urgency) === quadrant);
}
function getTodayTasks(tasks) {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tasks.filter((task)=>{
        // 包含今日截止的任务和未完成的高优先级任务
        const isToday = task.deadline <= tomorrow;
        const isHighPriority = task.importance >= 4 || task.urgency >= 4;
        const isPending = task.status === 'pending' || task.status === 'in-progress';
        return isPending && (isToday || isHighPriority);
    });
}
function sortTasksByPriority(tasks) {
    return [
        ...tasks
    ].sort((a, b)=>{
        const aQuadrant = classifyQuadrant(a.importance, a.urgency);
        const bQuadrant = classifyQuadrant(b.importance, b.urgency);
        // 先按象限排序
        if (aQuadrant !== bQuadrant) {
            return aQuadrant - bQuadrant;
        }
        // 再按重要性排序
        if (a.importance !== b.importance) {
            return b.importance - a.importance;
        }
        // 最后按紧急性排序
        return b.urgency - a.urgency;
    });
}
function sortTasksByDeadline(tasks) {
    return [
        ...tasks
    ].sort((a, b)=>a.deadline.getTime() - b.deadline.getTime());
}
function validateTask(task) {
    const errors = [];
    if (!task.title || task.title.trim().length === 0) {
        errors.push('任务标题不能为空');
    }
    if (task.title && task.title.length > 100) {
        errors.push('任务标题不能超过100个字符');
    }
    if (!task.category || !Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"]).includes(task.category)) {
        errors.push('请选择有效的任务分类');
    }
    if (!task.importance || task.importance < 1 || task.importance > 5) {
        errors.push('重要性必须在1-5之间');
    }
    if (!task.urgency || task.urgency < 1 || task.urgency > 5) {
        errors.push('紧急性必须在1-5之间');
    }
    if (!task.deadline) {
        errors.push('请设置截止时间');
    } else if (task.deadline < new Date()) {
        errors.push('截止时间不能早于当前时间');
    }
    if (!task.estimatedDuration || task.estimatedDuration < 15 || task.estimatedDuration > 480) {
        errors.push('预估时长必须在15分钟到8小时之间');
    }
    return errors;
}
function isTaskDueSoon(task, hoursThreshold = 24) {
    const now = new Date();
    const timeDiff = task.deadline.getTime() - now.getTime();
    const hoursDiff = timeDiff / (1000 * 60 * 60);
    return hoursDiff > 0 && hoursDiff <= hoursThreshold;
}
function isTaskOverdue(task) {
    return task.deadline < new Date() && task.status !== 'completed';
}
function getTaskUrgencyDescription(task) {
    if (isTaskOverdue(task)) {
        return '已过期';
    } else if (isTaskDueSoon(task, 2)) {
        return '2小时内到期';
    } else if (isTaskDueSoon(task, 24)) {
        return '今日到期';
    } else if (isTaskDueSoon(task, 48)) {
        return '明日到期';
    } else {
        return '充足时间';
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/shared/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 共享模块的统一导出
 * 提供类型、常量、工具函数的统一入口
 */ // ============================================================================
// 类型导出
// ============================================================================
__turbopack_context__.s({
    "createAppError": (()=>createAppError),
    "deepClone": (()=>deepClone),
    "hoursToMinutes": (()=>hoursToMinutes),
    "isAppError": (()=>isAppError),
    "isValidPriority": (()=>isValidPriority),
    "isValidQuadrant": (()=>isValidQuadrant),
    "isValidTaskCategory": (()=>isValidTaskCategory),
    "isValidTaskStatus": (()=>isValidTaskStatus),
    "minutesToHours": (()=>minutesToHours),
    "safeJsonParse": (()=>safeJsonParse)
});
// ============================================================================
// 常量导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-client] (ecmascript)");
// ============================================================================
// 工具函数导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-client] (ecmascript)");
;
;
;
function isValidTaskCategory(value) {
    return Object.values(TASK_CATEGORIES).includes(value);
}
function isValidPriority(value) {
    return typeof value === 'number' && value >= 1 && value <= 5;
}
function isValidTaskStatus(value) {
    return Object.values(TASK_STATUSES).includes(value);
}
function isValidQuadrant(value) {
    return typeof value === 'number' && value >= 1 && value <= 4;
}
function createAppError(code, message, details) {
    return {
        code,
        message,
        details,
        timestamp: new Date()
    };
}
function isAppError(error) {
    return error && typeof error.code === 'string' && typeof error.message === 'string' && error.timestamp instanceof Date;
}
function hoursToMinutes(hours) {
    return Math.round(hours * TIME_CONSTANTS.MINUTES_PER_HOUR);
}
function minutesToHours(minutes) {
    return minutes / TIME_CONSTANTS.MINUTES_PER_HOUR;
}
function safeJsonParse(json, defaultValue) {
    try {
        return JSON.parse(json);
    } catch  {
        return defaultValue;
    }
}
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    if (Array.isArray(obj)) {
        return obj.map((item)=>deepClone(item));
    }
    const cloned = {};
    for(const key in obj){
        if (obj.hasOwnProperty(key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    return cloned;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/shared/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/domains/intelligent-planning/algorithms/PlanningAlgorithm.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 时间规划算法
 * 负责根据任务优先级和用户时间配置生成智能的时间安排
 */ __turbopack_context__.s({
    "PlanningAlgorithm": (()=>PlanningAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-client] (ecmascript)");
;
class PlanningAlgorithm {
    /**
   * 生成今日时间安排
   */ generateDailySchedule(tasks, userTimeConfig) {
        // 1. 过滤今日需要处理的任务
        const todayTasks = this.filterTodayTasks(tasks);
        // 2. 计算分数并分类
        const scoredTasks = todayTasks.map((task)=>({
                ...task,
                score: this.calculateTaskScore(task),
                quadrant: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency)
            })).sort((a, b)=>{
            // 先按象限排序，再按分数排序
            if (a.quadrant !== b.quadrant) {
                return a.quadrant - b.quadrant;
            }
            return b.score - a.score;
        });
        // 3. 生成时间段（使用新的基于任务类型的算法）
        const timeSlots = this.generateTimeSlotsWithCategories(scoredTasks, userTimeConfig);
        // 4. 计算总时长
        const totalDuration = timeSlots.reduce((sum, slot)=>sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0);
        return {
            date: new Date(),
            timeSlots,
            totalTasks: todayTasks.length,
            estimatedDuration: totalDuration
        };
    }
    /**
   * 过滤今日需要处理的任务
   */ filterTodayTasks(tasks) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTodayTasks"])(tasks);
    }
    /**
   * 计算任务综合分数
   */ calculateTaskScore(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        // 基础分数：象限权重 * (重要性 + 紧急性)
        const baseScore = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ALGORITHM_CONSTANTS"].QUADRANT_WEIGHTS[quadrant] * (task.importance + task.urgency);
        // 截止日期权重
        const now = new Date();
        const timeToDeadline = task.deadline.getTime() - now.getTime();
        const hoursToDeadline = timeToDeadline / (1000 * 60 * 60);
        let deadlineWeight = 1;
        if (hoursToDeadline < 2) {
            deadlineWeight = 2.0; // 2小时内，权重翻倍
        } else if (hoursToDeadline < 24) {
            deadlineWeight = 1.5; // 24小时内，权重增加50%
        } else if (hoursToDeadline < 48) {
            deadlineWeight = 1.2; // 48小时内，权重增加20%
        }
        // 推迟惩罚
        const postponePenalty = task.postponeCount * __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ALGORITHM_CONSTANTS"].POSTPONE_PENALTY;
        // 分类权重
        const categoryWeight = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ALGORITHM_CONSTANTS"].CATEGORY_WEIGHTS[task.category];
        return baseScore * deadlineWeight * categoryWeight * (1 + postponePenalty);
    }
    /**
   * 基于任务类型生成时间段安排
   */ generateTimeSlotsWithCategories(tasks, userTimeConfig) {
        const timeSlots = [];
        const today = new Date();
        // 使用默认配置如果没有提供用户配置
        const config = userTimeConfig || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_USER_CONFIG"];
        // 按任务类型分组
        const tasksByCategory = this.groupTasksByCategory(tasks);
        // 为每种类型的任务安排时间
        for (const [category, categoryTasks] of Object.entries(tasksByCategory)){
            if (categoryTasks.length === 0) continue;
            const categoryPrefs = config.categoryPreferences[category];
            if (!categoryPrefs) continue;
            // 为该类型任务生成可用时间段
            const availableSlots = this.generateAvailableSlots(categoryPrefs.preferredTimes, config.fixedSlots, today);
            // 在可用时间段中安排任务
            this.scheduleTasksInSlots(categoryTasks, availableSlots, timeSlots);
        }
        return timeSlots.sort((a, b)=>a.startTime.getTime() - b.startTime.getTime());
    }
    /**
   * 按任务类型分组
   */ groupTasksByCategory(tasks) {
        return {
            work: tasks.filter((task)=>task.category === 'work'),
            improvement: tasks.filter((task)=>task.category === 'improvement'),
            entertainment: tasks.filter((task)=>task.category === 'entertainment')
        };
    }
    /**
   * 生成可用时间段
   */ generateAvailableSlots(preferredTimes, fixedSlots, date) {
        const availableSlots = [];
        for (const timeRange of preferredTimes){
            const [startTime, endTime] = timeRange.split('-');
            const [startHour, startMinute] = startTime.split(':').map(Number);
            const [endHour, endMinute] = endTime.split(':').map(Number);
            const slotStart = new Date(date);
            slotStart.setHours(startHour, startMinute, 0, 0);
            const slotEnd = new Date(date);
            slotEnd.setHours(endHour, endMinute, 0, 0);
            // 检查是否与固定时间段冲突
            let hasConflict = false;
            for (const fixedSlot of fixedSlots){
                const [fixedStartHour, fixedStartMinute] = fixedSlot.start.split(':').map(Number);
                const [fixedEndHour, fixedEndMinute] = fixedSlot.end.split(':').map(Number);
                const fixedStart = new Date(date);
                fixedStart.setHours(fixedStartHour, fixedStartMinute, 0, 0);
                const fixedEnd = new Date(date);
                fixedEnd.setHours(fixedEndHour, fixedEndMinute, 0, 0);
                // 检查时间段重叠
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTimeRangeOverlap"])(slotStart, slotEnd, fixedStart, fixedEnd)) {
                    hasConflict = true;
                    break;
                }
            }
            if (!hasConflict) {
                availableSlots.push({
                    start: slotStart,
                    end: slotEnd
                });
            }
        }
        return availableSlots;
    }
    /**
   * 在可用时间段中安排任务
   */ scheduleTasksInSlots(tasks, availableSlots, timeSlots) {
        let currentSlotIndex = 0;
        let currentTime = availableSlots[0]?.start;
        if (!currentTime) return;
        for (const task of tasks){
            const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒
            // 寻找合适的时间段
            while(currentSlotIndex < availableSlots.length){
                const currentSlot = availableSlots[currentSlotIndex];
                const remainingTime = currentSlot.end.getTime() - currentTime.getTime();
                if (remainingTime >= taskDuration) {
                    // 在当前时间段安排任务
                    const endTime = new Date(currentTime.getTime() + taskDuration);
                    timeSlots.push({
                        task,
                        startTime: new Date(currentTime),
                        endTime,
                        isFixed: false
                    });
                    // 更新当前时间，添加15分钟休息时间
                    currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);
                    break;
                } else {
                    // 移动到下一个时间段
                    currentSlotIndex++;
                    currentTime = availableSlots[currentSlotIndex]?.start;
                    if (!currentTime) break;
                }
            }
        }
    }
    /**
   * 获取任务建议
   */ getTaskRecommendation(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT) {
            return '🔥 高优先级任务，建议立即处理';
        } else if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT) {
            return '📅 重要任务，建议合理安排时间';
        } else if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT) {
            return '⚡ 紧急但不重要，考虑委托或快速处理';
        } else {
            return '🤔 优先级较低，可以延后或删除';
        }
    }
    /**
   * 生成时间段安排（保留原方法作为后备）
   */ generateTimeSlots(tasks, workHours) {
        const timeSlots = [];
        const today = new Date();
        // 解析工作时间
        const [startHour, startMinute] = workHours.start.split(':').map(Number);
        const [endHour, endMinute] = workHours.end.split(':').map(Number);
        let currentTime = new Date(today);
        currentTime.setHours(startHour, startMinute, 0, 0);
        const workEndTime = new Date(today);
        workEndTime.setHours(endHour, endMinute, 0, 0);
        for (const task of tasks){
            // 检查是否还有足够的工作时间
            const remainingWorkTime = workEndTime.getTime() - currentTime.getTime();
            const taskDuration = (task.estimatedDuration || 60) * 60 * 1000; // 转换为毫秒
            if (remainingWorkTime < taskDuration) {
                continue;
            }
            const endTime = new Date(currentTime.getTime() + taskDuration);
            timeSlots.push({
                task,
                startTime: new Date(currentTime),
                endTime,
                isFixed: false
            });
            // 更新当前时间，添加15分钟休息时间
            currentTime = new Date(endTime.getTime() + 15 * 60 * 1000);
            // 如果超过工作时间，停止安排
            if (currentTime >= workEndTime) {
                break;
            }
        }
        return timeSlots;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/intelligent-planning/algorithms/BalanceAlgorithm.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 生活平衡算法
 * 分析用户的时间分配，提供生活平衡建议
 */ __turbopack_context__.s({
    "BalanceAlgorithm": (()=>BalanceAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-client] (ecmascript)");
;
class BalanceAlgorithm {
    /**
   * 分析用户的生活平衡状况
   */ async analyzeBalance(userId, date = new Date()) {
        // 获取本周的统计数据
        const weeklyStats = await this.calculateWeeklyStats(userId, date);
        // 计算分类比例
        const categoryRatios = this.calculateCategoryRatios(weeklyStats.totalTime);
        // 计算平衡分数
        const balanceScore = this.calculateBalanceScore(categoryRatios);
        // 生成建议
        const recommendations = this.generateRecommendations(categoryRatios, weeklyStats);
        return {
            userId,
            date,
            categoryRatios,
            weeklyStats,
            recommendations,
            balanceScore
        };
    }
    /**
   * 计算本周统计数据
   */ async calculateWeeklyStats(userId, date) {
        const startOfWeek = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStartOfWeek"])(date);
        const endOfWeek = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getEndOfWeek"])(date);
        // 这里应该从数据库获取数据，暂时使用模拟数据
        const mockStats = {
            work: 2400,
            improvement: 420,
            entertainment: 600 // 10小时
        };
        const totalMinutes = mockStats.work + mockStats.improvement + mockStats.entertainment;
        const averageDaily = {
            work: mockStats.work / 7,
            improvement: mockStats.improvement / 7,
            entertainment: mockStats.entertainment / 7
        };
        // 计算趋势（这里简化处理）
        const trend = this.calculateTrend(mockStats);
        return {
            totalTime: mockStats,
            averageDaily,
            trend
        };
    }
    /**
   * 计算分类比例
   */ calculateCategoryRatios(totalTime) {
        const total = totalTime.work + totalTime.improvement + totalTime.entertainment;
        if (total === 0) {
            return {
                work: 0,
                improvement: 0,
                entertainment: 0
            };
        }
        return {
            work: Math.round(totalTime.work / total * 100) / 100,
            improvement: Math.round(totalTime.improvement / total * 100) / 100,
            entertainment: Math.round(totalTime.entertainment / total * 100) / 100
        };
    }
    /**
   * 计算平衡分数 (0-100)
   */ calculateBalanceScore(ratios) {
        // 理想比例：工作60%，提升25%，娱乐15%
        const idealRatios = {
            work: 0.6,
            improvement: 0.25,
            entertainment: 0.15
        };
        // 计算与理想比例的偏差
        const workDeviation = Math.abs(ratios.work - idealRatios.work);
        const improvementDeviation = Math.abs(ratios.improvement - idealRatios.improvement);
        const entertainmentDeviation = Math.abs(ratios.entertainment - idealRatios.entertainment);
        // 总偏差
        const totalDeviation = workDeviation + improvementDeviation + entertainmentDeviation;
        // 转换为分数 (偏差越小，分数越高)
        const score = Math.max(0, 100 - totalDeviation * 100);
        return Math.round(score);
    }
    /**
   * 计算趋势
   */ calculateTrend(currentStats) {
        // 这里应该比较本周与上周的数据
        // 暂时返回稳定状态
        return 'stable';
    }
    /**
   * 生成平衡建议
   */ generateRecommendations(ratios, weeklyStats) {
        const recommendations = [];
        // 工作时间建议
        if (ratios.work > 0.7) {
            recommendations.push('工作时间占比过高，建议适当减少工作量，增加休息时间');
        } else if (ratios.work < 0.5) {
            recommendations.push('工作时间占比较低，可以考虑提高工作效率或增加工作时间');
        }
        // 提升时间建议
        if (ratios.improvement < 0.15) {
            recommendations.push('个人提升时间不足，建议每天安排至少1-2小时用于学习和成长');
        } else if (ratios.improvement > 0.35) {
            recommendations.push('个人提升时间充足，保持良好的学习习惯');
        }
        // 娱乐时间建议
        if (ratios.entertainment < 0.1) {
            recommendations.push('娱乐时间过少，适当的放松有助于提高工作效率');
        } else if (ratios.entertainment > 0.25) {
            recommendations.push('娱乐时间较多，可以考虑将部分时间用于工作或学习');
        }
        // 平衡性建议
        const balanceScore = this.calculateBalanceScore(ratios);
        if (balanceScore >= 80) {
            recommendations.push('时间分配很均衡，继续保持！');
        } else if (balanceScore >= 60) {
            recommendations.push('时间分配基本合理，可以进行微调优化');
        } else {
            recommendations.push('时间分配需要调整，建议重新规划各类活动的时间比例');
        }
        return recommendations;
    }
    /**
   * 更新今日统计
   */ async updateTodayStats(userId, category, duration) {
        const today = new Date();
        const dateStr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDate"])(today);
        // 这里应该更新数据库中的统计数据
        console.log(`更新统计: 用户${userId}, 日期${dateStr}, 分类${category}, 时长${duration}分钟`);
    // 模拟数据库操作
    // await supabase.from('daily_stats').upsert({
    //   user_id: userId,
    //   date: dateStr,
    //   [category + '_time']: duration
    // });
    }
    /**
   * 获取分类时间建议
   */ getCategoryTimeRecommendation(category, currentRatio) {
        const recommendations = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK]: {
                low: '工作时间不足，建议增加专注工作的时间',
                normal: '工作时间合理，保持当前节奏',
                high: '工作时间过长，注意劳逸结合'
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].IMPROVEMENT]: {
                low: '学习时间不足，建议每天安排固定的学习时间',
                normal: '学习时间充足，继续保持学习习惯',
                high: '学习时间很充足，可以考虑实践应用'
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT]: {
                low: '娱乐时间过少，适当放松有助于身心健康',
                normal: '娱乐时间合适，保持工作生活平衡',
                high: '娱乐时间较多，可以考虑更多有意义的活动'
            }
        };
        let level;
        if (currentRatio < 0.2) {
            level = 'low';
        } else if (currentRatio > 0.6) {
            level = 'high';
        } else {
            level = 'normal';
        }
        return recommendations[category][level];
    }
    /**
   * 预测下周建议
   */ predictNextWeekRecommendations(currentStats) {
        const recommendations = [];
        // 基于当前趋势预测
        if (currentStats.trend === 'improving') {
            recommendations.push('本周平衡状况在改善，继续保持当前的时间安排');
        } else if (currentStats.trend === 'declining') {
            recommendations.push('本周平衡状况在下降，建议调整时间分配策略');
        } else {
            recommendations.push('本周时间分配稳定，可以尝试优化某些细节');
        }
        // 基于平均时间给出建议
        const { averageDaily } = currentStats;
        const total = averageDaily.work + averageDaily.improvement + averageDaily.entertainment;
        if (total < 480) {
            recommendations.push('每日活跃时间较少，建议增加有意义的活动');
        } else if (total > 720) {
            recommendations.push('每日活跃时间较长，注意适当休息');
        }
        return recommendations;
    }
    /**
   * 获取平衡分数等级描述
   */ getBalanceScoreDescription(score) {
        if (score >= 90) {
            return '优秀 - 时间分配非常均衡';
        } else if (score >= 80) {
            return '良好 - 时间分配基本均衡';
        } else if (score >= 70) {
            return '一般 - 时间分配需要小幅调整';
        } else if (score >= 60) {
            return '待改善 - 时间分配需要调整';
        } else {
            return '需要改进 - 时间分配严重失衡';
        }
    }
    /**
   * 计算理想的时间分配建议
   */ calculateIdealTimeAllocation(availableHours) {
        const totalMinutes = availableHours * 60;
        return {
            work: Math.round(totalMinutes * 0.6),
            improvement: Math.round(totalMinutes * 0.25),
            entertainment: Math.round(totalMinutes * 0.15) // 15%
        };
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/intelligent-planning/algorithms/FixAlgorithm.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 修复算法
 * 检测和处理推迟的任务，提供修复建议
 */ __turbopack_context__.s({
    "FixAlgorithm": (()=>FixAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-client] (ecmascript)");
;
class FixAlgorithm {
    /**
   * 分析推迟的任务
   */ analyzePostponedTasks(tasks) {
        const postponedTasks = tasks.filter((task)=>task.postponeCount > 0 || task.status === 'postponed' || (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task));
        return postponedTasks.map((task)=>this.createPostponedAlert(task));
    }
    /**
   * 创建推迟任务提醒
   */ createPostponedAlert(task) {
        const daysSinceCreated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDaysDifference"])(task.createdAt, new Date());
        const severity = this.calculateSeverity(task);
        const recommendation = this.generateRecommendation(task);
        return {
            taskId: task.id,
            task,
            postponeCount: task.postponeCount,
            lastPostponeDate: task.updatedAt,
            severity,
            recommendation
        };
    }
    /**
   * 计算严重程度
   */ calculateSeverity(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        const isOverdue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task);
        const postponeCount = task.postponeCount;
        // 已过期的任务
        if (isOverdue) {
            if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT) {
                return 'high';
            } else if (quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT || quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT) {
                return 'medium';
            } else {
                return 'low';
            }
        }
        // 根据推迟次数判断
        if (postponeCount >= 3) {
            return quadrant <= 2 ? 'high' : 'medium';
        } else if (postponeCount >= 2) {
            return quadrant === 1 ? 'high' : 'medium';
        } else {
            return quadrant === 1 ? 'medium' : 'low';
        }
    }
    /**
   * 生成修复建议
   */ generateRecommendation(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        const isOverdue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task);
        const postponeCount = task.postponeCount;
        if (isOverdue) {
            return this.getOverdueRecommendation(task, quadrant);
        }
        if (postponeCount >= 3) {
            return this.getHighPostponeRecommendation(task, quadrant);
        }
        if (postponeCount >= 1) {
            return this.getPostponeRecommendation(task, quadrant);
        }
        return '建议尽快处理此任务';
    }
    /**
   * 获取过期任务建议
   */ getOverdueRecommendation(task, quadrant) {
        const daysPastDeadline = Math.abs((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDaysDifference"])(task.deadline, new Date()));
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return `🚨 任务已过期${daysPastDeadline}天，需要立即处理！考虑重新评估截止时间或寻求帮助。`;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                return `⚠️ 重要任务已过期${daysPastDeadline}天，建议重新安排优先级，尽快完成。`;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                return `⏰ 紧急任务已过期${daysPastDeadline}天，考虑委托他人处理或重新评估必要性。`;
            default:
                return `📅 任务已过期${daysPastDeadline}天，建议评估是否仍需完成，或者删除此任务。`;
        }
    }
    /**
   * 获取高频推迟任务建议
   */ getHighPostponeRecommendation(task, quadrant) {
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return `🔥 此任务已推迟${task.postponeCount}次，建议分解为更小的子任务，或寻求帮助完成。`;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                return `📋 重要任务推迟${task.postponeCount}次，建议设定固定时间块专门处理，避免再次推迟。`;
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                return `🤝 紧急任务推迟${task.postponeCount}次，强烈建议委托他人处理或使用自动化工具。`;
            default:
                return `🤔 任务推迟${task.postponeCount}次，建议重新评估其必要性，考虑删除或降低优先级。`;
        }
    }
    /**
   * 获取一般推迟任务建议
   */ getPostponeRecommendation(task, quadrant) {
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return '🎯 高优先级任务，建议立即安排时间处理，避免进一步推迟。';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                return '📅 重要任务，建议在日程中安排固定时间，确保按时完成。';
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                return '⚡ 考虑委托他人处理，或寻找更高效的解决方案。';
            default:
                return '💭 评估任务的实际价值，考虑是否需要继续保留。';
        }
    }
    /**
   * 获取推迟任务的统计信息
   */ getPostponedTasksStats(tasks) {
        const postponedTasks = tasks.filter((task)=>task.postponeCount > 0 || task.status === 'postponed');
        const byCategory = postponedTasks.reduce((acc, task)=>{
            acc[task.category] = (acc[task.category] || 0) + 1;
            return acc;
        }, {});
        const alerts = this.analyzePostponedTasks(postponedTasks);
        const bySeverity = alerts.reduce((acc, alert)=>{
            acc[alert.severity] = (acc[alert.severity] || 0) + 1;
            return acc;
        }, {});
        const totalPostponeCount = postponedTasks.reduce((sum, task)=>sum + task.postponeCount, 0);
        const averagePostponeCount = postponedTasks.length > 0 ? Math.round(totalPostponeCount / postponedTasks.length * 10) / 10 : 0;
        return {
            totalPostponed: postponedTasks.length,
            byCategory,
            bySeverity,
            averagePostponeCount
        };
    }
    /**
   * 建议任务重新安排策略
   */ suggestRescheduleStrategy(task) {
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        const postponeCount = task.postponeCount;
        // 高频推迟的任务需要特殊处理
        if (postponeCount >= 3) {
            return this.getHighPostponeStrategy(task, quadrant);
        }
        // 一般推迟任务的策略
        return this.getGeneralStrategy(task, quadrant);
    }
    /**
   * 获取高频推迟任务的策略
   */ getHighPostponeStrategy(task, quadrant) {
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return {
                    strategy: 'immediate_action',
                    breakDown: [
                        '将任务分解为15-30分钟的小块',
                        '立即开始第一个小任务',
                        '寻求同事或朋友的帮助',
                        '移除所有干扰因素'
                    ]
                };
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                const newDeadline = new Date();
                newDeadline.setDate(newDeadline.getDate() + 7);
                return {
                    strategy: 'scheduled_focus',
                    newDeadline,
                    breakDown: [
                        '设定每日固定时间处理',
                        '使用番茄工作法',
                        '设置进度检查点',
                        '奖励机制激励完成'
                    ]
                };
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                return {
                    strategy: 'delegate_or_automate',
                    delegation: '寻找可以委托的人员或自动化工具',
                    breakDown: [
                        '评估委托的可能性',
                        '寻找自动化解决方案',
                        '如无法委托，快速批量处理'
                    ]
                };
            default:
                return {
                    strategy: 'eliminate_or_defer',
                    breakDown: [
                        '重新评估任务的必要性',
                        '考虑完全删除此任务',
                        '如必须保留，设定更宽松的时间线'
                    ]
                };
        }
    }
    /**
   * 获取一般任务的重新安排策略
   */ getGeneralStrategy(task, quadrant) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        switch(quadrant){
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                return {
                    strategy: 'priority_scheduling',
                    newDeadline: tomorrow,
                    breakDown: [
                        '安排在最佳工作时间',
                        '清除其他干扰',
                        '专注完成'
                    ]
                };
            case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                const nextWeek = new Date();
                nextWeek.setDate(nextWeek.getDate() + 7);
                return {
                    strategy: 'planned_execution',
                    newDeadline: nextWeek,
                    breakDown: [
                        '制定详细计划',
                        '分阶段执行',
                        '定期检查进度'
                    ]
                };
            default:
                return {
                    strategy: 'flexible_scheduling',
                    breakDown: [
                        '安排在空闲时间',
                        '与其他类似任务批量处理'
                    ]
                };
        }
    }
    /**
   * 检查任务是否需要紧急关注
   */ needsUrgentAttention(task) {
        const isOverdue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task);
        const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
        const highPostponeCount = task.postponeCount >= 3;
        return isOverdue || quadrant === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT && task.postponeCount >= 1 || highPostponeCount && quadrant <= 2;
    }
    /**
   * 生成修复行动计划
   */ generateActionPlan(alerts) {
        const actionPlan = [];
        const highSeverityTasks = alerts.filter((alert)=>alert.severity === 'high');
        const mediumSeverityTasks = alerts.filter((alert)=>alert.severity === 'medium');
        if (highSeverityTasks.length > 0) {
            actionPlan.push(`🚨 立即处理 ${highSeverityTasks.length} 个高优先级推迟任务`);
        }
        if (mediumSeverityTasks.length > 0) {
            actionPlan.push(`⚠️ 本周内处理 ${mediumSeverityTasks.length} 个中等优先级推迟任务`);
        }
        if (alerts.length > 5) {
            actionPlan.push('📋 考虑重新评估任务管理策略，避免过多任务推迟');
        }
        return actionPlan;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/intelligent-planning/algorithms/TimeAdjustmentAlgorithm.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 时间调整算法
 * 当任务超时时，动态调整后续任务的时间安排
 */ __turbopack_context__.s({
    "TimeAdjustmentAlgorithm": (()=>TimeAdjustmentAlgorithm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/dateUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-client] (ecmascript)");
;
class TimeAdjustmentAlgorithm {
    /**
   * 当任务超时时调整后续安排
   */ adjustForOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig) {
        const originalDuration = overrunTask.estimatedDuration;
        const overrunMinutes = actualDuration - originalDuration;
        if (overrunMinutes <= 0) {
            return {
                success: true,
                adjustedSchedule: currentSchedule,
                affectedTasks: [],
                message: '任务按时完成，无需调整',
                impactScore: 0
            };
        }
        // 找到超时任务在当前安排中的位置
        const overrunSlotIndex = currentSchedule.findIndex((slot)=>slot.task.id === overrunTask.id);
        if (overrunSlotIndex === -1) {
            return {
                success: false,
                adjustedSchedule: currentSchedule,
                affectedTasks: [],
                message: '未找到超时任务的时间安排',
                impactScore: 0
            };
        }
        // 计算调整策略
        const adjustmentStrategy = this.calculateAdjustmentStrategy(overrunMinutes, currentSchedule, overrunSlotIndex, userTimeConfig);
        return this.applyAdjustmentStrategy(adjustmentStrategy, currentSchedule, overrunSlotIndex, overrunMinutes);
    }
    /**
   * 计算调整策略
   */ calculateAdjustmentStrategy(overrunMinutes, schedule, overrunIndex, userTimeConfig) {
        const remainingSlots = schedule.slice(overrunIndex + 1);
        if (remainingSlots.length === 0) {
            return 'postpone';
        }
        // 计算可压缩的时间
        const compressibleTime = this.calculateCompressibleTime(remainingSlots);
        if (compressibleTime >= overrunMinutes) {
            return 'compress';
        } else if (compressibleTime >= overrunMinutes * 0.6) {
            return 'hybrid'; // 部分压缩，部分推迟
        } else {
            return 'reschedule';
        }
    }
    /**
   * 计算可压缩的时间
   */ calculateCompressibleTime(slots) {
        let compressibleTime = 0;
        for (const slot of slots){
            const task = slot.task;
            const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
            const duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMinutesBetween"])(slot.startTime, slot.endTime);
            // 根据任务重要性确定可压缩比例
            let compressRatio = 0;
            switch(quadrant){
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                    compressRatio = 0.1; // 最多压缩10%
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                    compressRatio = 0.2; // 最多压缩20%
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                    compressRatio = 0.3; // 最多压缩30%
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT:
                    compressRatio = 0.5; // 最多压缩50%
                    break;
            }
            compressibleTime += duration * compressRatio;
        }
        return Math.floor(compressibleTime);
    }
    /**
   * 应用调整策略
   */ applyAdjustmentStrategy(strategy, schedule, overrunIndex, overrunMinutes) {
        const adjustedSchedule = [
            ...schedule
        ];
        const affectedTasks = [];
        let impactScore = 0;
        switch(strategy){
            case 'compress':
                return this.applyCompressionStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
            case 'postpone':
                return this.applyPostponeStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
            case 'reschedule':
                return this.applyRescheduleStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
            case 'hybrid':
                return this.applyHybridStrategy(adjustedSchedule, overrunIndex, overrunMinutes);
            default:
                return {
                    success: false,
                    adjustedSchedule: schedule,
                    affectedTasks: [],
                    message: '未知的调整策略',
                    impactScore: 0
                };
        }
    }
    /**
   * 应用压缩策略
   */ applyCompressionStrategy(schedule, overrunIndex, overrunMinutes) {
        const affectedTasks = [];
        let remainingOverrun = overrunMinutes;
        let impactScore = 0;
        // 延长超时任务的结束时间
        schedule[overrunIndex].endTime = new Date(schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000);
        // 压缩后续任务
        for(let i = overrunIndex + 1; i < schedule.length && remainingOverrun > 0; i++){
            const slot = schedule[i];
            const task = slot.task;
            const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
            const duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMinutesBetween"])(slot.startTime, slot.endTime);
            // 计算压缩比例
            let compressRatio = 0;
            switch(quadrant){
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_IMPORTANT:
                    compressRatio = 0.1;
                    impactScore += 10;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].IMPORTANT_NOT_URGENT:
                    compressRatio = 0.2;
                    impactScore += 6;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].URGENT_NOT_IMPORTANT:
                    compressRatio = 0.3;
                    impactScore += 4;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUADRANTS"].NOT_URGENT_NOT_IMPORTANT:
                    compressRatio = 0.5;
                    impactScore += 2;
                    break;
            }
            const maxCompress = Math.floor(duration * compressRatio);
            const actualCompress = Math.min(maxCompress, remainingOverrun);
            if (actualCompress > 0) {
                // 调整开始时间（向后推迟）
                slot.startTime = new Date(slot.startTime.getTime() + (remainingOverrun - actualCompress) * 60 * 1000);
                // 调整结束时间（压缩持续时间）
                slot.endTime = new Date(slot.endTime.getTime() + (remainingOverrun - actualCompress) * 60 * 1000 - actualCompress * 60 * 1000);
                affectedTasks.push(task);
                remainingOverrun -= actualCompress;
            } else {
                // 只是向后推迟，不压缩
                slot.startTime = new Date(slot.startTime.getTime() + remainingOverrun * 60 * 1000);
                slot.endTime = new Date(slot.endTime.getTime() + remainingOverrun * 60 * 1000);
            }
        }
        return {
            success: remainingOverrun === 0,
            adjustedSchedule: schedule,
            affectedTasks,
            message: remainingOverrun === 0 ? `成功通过压缩后续任务调整了${overrunMinutes}分钟的超时` : `部分调整成功，仍有${remainingOverrun}分钟需要其他处理`,
            impactScore
        };
    }
    /**
   * 应用推迟策略
   */ applyPostponeStrategy(schedule, overrunIndex, overrunMinutes) {
        // 延长超时任务的结束时间
        schedule[overrunIndex].endTime = new Date(schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000);
        // 将后续所有任务向后推迟
        for(let i = overrunIndex + 1; i < schedule.length; i++){
            schedule[i].startTime = new Date(schedule[i].startTime.getTime() + overrunMinutes * 60 * 1000);
            schedule[i].endTime = new Date(schedule[i].endTime.getTime() + overrunMinutes * 60 * 1000);
        }
        const affectedTasks = schedule.slice(overrunIndex + 1).map((slot)=>slot.task);
        return {
            success: true,
            adjustedSchedule: schedule,
            affectedTasks,
            message: `所有后续任务向后推迟${overrunMinutes}分钟`,
            impactScore: affectedTasks.length * 3 // 推迟的影响相对较小
        };
    }
    /**
   * 应用重新安排策略
   */ applyRescheduleStrategy(schedule, overrunIndex, overrunMinutes) {
        const affectedTasks = [];
        const remainingSlots = schedule.slice(overrunIndex + 1);
        // 延长超时任务
        schedule[overrunIndex].endTime = new Date(schedule[overrunIndex].endTime.getTime() + overrunMinutes * 60 * 1000);
        // 选择低优先级任务移到明天
        const tasksToReschedule = this.selectTasksToReschedule(remainingSlots, overrunMinutes);
        // 移除被重新安排的任务
        const newSchedule = schedule.filter((slot)=>!tasksToReschedule.some((task)=>task.id === slot.task.id));
        // 调整剩余任务的时间
        let timeOffset = overrunMinutes;
        for(let i = overrunIndex + 1; i < newSchedule.length; i++){
            newSchedule[i].startTime = new Date(newSchedule[i].startTime.getTime() + timeOffset * 60 * 1000);
            newSchedule[i].endTime = new Date(newSchedule[i].endTime.getTime() + timeOffset * 60 * 1000);
        }
        return {
            success: true,
            adjustedSchedule: newSchedule,
            affectedTasks: tasksToReschedule,
            message: `${tasksToReschedule.length}个低优先级任务被重新安排到明天`,
            impactScore: tasksToReschedule.length * 8 // 重新安排的影响较大
        };
    }
    /**
   * 应用混合策略
   */ applyHybridStrategy(schedule, overrunIndex, overrunMinutes) {
        // 先尝试压缩
        const compressResult = this.applyCompressionStrategy([
            ...schedule
        ], overrunIndex, overrunMinutes);
        if (compressResult.success) {
            return compressResult;
        }
        // 如果压缩不够，再结合重新安排
        const remainingOverrun = overrunMinutes - this.calculateCompressibleTime(schedule.slice(overrunIndex + 1));
        const rescheduleResult = this.applyRescheduleStrategy([
            ...schedule
        ], overrunIndex, remainingOverrun);
        return {
            success: true,
            adjustedSchedule: rescheduleResult.adjustedSchedule,
            affectedTasks: [
                ...compressResult.affectedTasks,
                ...rescheduleResult.affectedTasks
            ],
            message: `采用混合策略：压缩部分任务，重新安排${rescheduleResult.affectedTasks.length}个任务`,
            impactScore: compressResult.impactScore + rescheduleResult.impactScore
        };
    }
    /**
   * 选择需要重新安排的任务
   */ selectTasksToReschedule(slots, targetMinutes) {
        // 按优先级排序，选择低优先级任务
        const sortedSlots = [
            ...slots
        ].sort((a, b)=>{
            const aQuadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(a.task.importance, a.task.urgency);
            const bQuadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(b.task.importance, b.task.urgency);
            return bQuadrant - aQuadrant; // 降序，低优先级在前
        });
        const tasksToReschedule = [];
        let freedTime = 0;
        for (const slot of sortedSlots){
            if (freedTime >= targetMinutes) break;
            const duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$dateUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMinutesBetween"])(slot.startTime, slot.endTime);
            tasksToReschedule.push(slot.task);
            freedTime += duration;
        }
        return tasksToReschedule;
    }
    /**
   * 预测调整的影响
   */ predictAdjustmentImpact(overrunMinutes, schedule, overrunIndex) {
        const remainingSlots = schedule.slice(overrunIndex + 1);
        const compressibleTime = this.calculateCompressibleTime(remainingSlots);
        if (compressibleTime >= overrunMinutes) {
            return {
                strategy: 'compress',
                affectedTasksCount: remainingSlots.length,
                impactScore: remainingSlots.length * 3,
                description: '通过压缩后续任务时间来调整'
            };
        } else if (remainingSlots.length === 0) {
            return {
                strategy: 'postpone',
                affectedTasksCount: 0,
                impactScore: 0,
                description: '无后续任务，无需调整'
            };
        } else {
            const tasksToReschedule = this.selectTasksToReschedule(remainingSlots, overrunMinutes);
            return {
                strategy: 'reschedule',
                affectedTasksCount: tasksToReschedule.length,
                impactScore: tasksToReschedule.length * 8,
                description: `${tasksToReschedule.length}个任务将被重新安排`
            };
        }
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/intelligent-planning/coordinators/AlgorithmCoordinator.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 算法协调器
 * 统一管理和协调所有智能规划算法
 */ __turbopack_context__.s({
    "AlgorithmCoordinator": (()=>AlgorithmCoordinator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/PlanningAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/BalanceAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/FixAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/TimeAdjustmentAlgorithm.ts [app-client] (ecmascript)");
;
;
;
;
class AlgorithmCoordinator {
    planningAlgorithm;
    balanceAlgorithm;
    fixAlgorithm;
    timeAdjustmentAlgorithm;
    constructor(){
        this.planningAlgorithm = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlanningAlgorithm"]();
        this.balanceAlgorithm = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BalanceAlgorithm"]();
        this.fixAlgorithm = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FixAlgorithm"]();
        this.timeAdjustmentAlgorithm = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TimeAdjustmentAlgorithm"]();
    }
    // ============================================================================
    // 时间规划相关方法
    // ============================================================================
    /**
   * 生成智能的每日时间安排
   */ async generateDailySchedule(tasks, userTimeConfig) {
        try {
            // 1. 检查推迟任务并给出警告
            const postponedAlerts = this.fixAlgorithm.analyzePostponedTasks(tasks);
            const urgentAlerts = postponedAlerts.filter((alert)=>this.fixAlgorithm.needsUrgentAttention(alert.task));
            // 2. 生成基础时间安排
            const schedule = this.planningAlgorithm.generateDailySchedule(tasks, userTimeConfig);
            // 3. 如果有紧急推迟任务，调整安排优先级
            if (urgentAlerts.length > 0) {
                console.log(`检测到 ${urgentAlerts.length} 个需要紧急处理的推迟任务`);
            // 这里可以进一步优化安排逻辑
            }
            return schedule;
        } catch (error) {
            console.error('生成每日安排时出错:', error);
            throw new Error('无法生成每日时间安排');
        }
    }
    /**
   * 获取任务建议
   */ getTaskRecommendation(task) {
        return this.planningAlgorithm.getTaskRecommendation(task);
    }
    // ============================================================================
    // 生活平衡分析相关方法
    // ============================================================================
    /**
   * 分析用户的生活平衡状况
   */ async analyzeLifeBalance(userId, date) {
        try {
            return await this.balanceAlgorithm.analyzeBalance(userId, date);
        } catch (error) {
            console.error('分析生活平衡时出错:', error);
            throw new Error('无法分析生活平衡状况');
        }
    }
    /**
   * 更新今日统计数据
   */ async updateTodayStats(userId, category, duration) {
        try {
            await this.balanceAlgorithm.updateTodayStats(userId, category, duration);
        } catch (error) {
            console.error('更新统计数据时出错:', error);
            throw new Error('无法更新统计数据');
        }
    }
    /**
   * 获取分类时间建议
   */ getCategoryTimeRecommendation(category, currentRatio) {
        return this.balanceAlgorithm.getCategoryTimeRecommendation(category, currentRatio);
    }
    // ============================================================================
    // 任务修复相关方法
    // ============================================================================
    /**
   * 分析推迟的任务
   */ analyzePostponedTasks(tasks) {
        return this.fixAlgorithm.analyzePostponedTasks(tasks);
    }
    /**
   * 获取推迟任务统计
   */ getPostponedTasksStats(tasks) {
        return this.fixAlgorithm.getPostponedTasksStats(tasks);
    }
    /**
   * 建议任务重新安排策略
   */ suggestRescheduleStrategy(task) {
        return this.fixAlgorithm.suggestRescheduleStrategy(task);
    }
    /**
   * 生成修复行动计划
   */ generateActionPlan(alerts) {
        return this.fixAlgorithm.generateActionPlan(alerts);
    }
    // ============================================================================
    // 时间调整相关方法
    // ============================================================================
    /**
   * 处理任务超时调整
   */ async handleTaskOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig) {
        try {
            const result = this.timeAdjustmentAlgorithm.adjustForOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig);
            // 如果调整成功，更新统计数据
            if (result.success) {
                await this.balanceAlgorithm.updateTodayStats(overrunTask.userId, overrunTask.category, actualDuration);
            }
            return result;
        } catch (error) {
            console.error('处理任务超时时出错:', error);
            throw new Error('无法处理任务超时调整');
        }
    }
    /**
   * 预测调整影响
   */ predictAdjustmentImpact(overrunMinutes, schedule, overrunIndex) {
        return this.timeAdjustmentAlgorithm.predictAdjustmentImpact(overrunMinutes, schedule, overrunIndex);
    }
    // ============================================================================
    // 综合分析和建议
    // ============================================================================
    /**
   * 生成综合的每日建议
   */ async generateDailyInsights(userId, tasks, userTimeConfig) {
        try {
            // 并行执行多个分析
            const [schedule, balanceAnalysis, postponedAlerts] = await Promise.all([
                this.generateDailySchedule(tasks, userTimeConfig),
                this.analyzeLifeBalance(userId),
                Promise.resolve(this.analyzePostponedTasks(tasks))
            ]);
            // 生成综合建议
            const recommendations = this.generateComprehensiveRecommendations(schedule, balanceAnalysis, postponedAlerts);
            return {
                schedule,
                balanceAnalysis,
                postponedAlerts,
                recommendations
            };
        } catch (error) {
            console.error('生成每日洞察时出错:', error);
            throw new Error('无法生成每日洞察');
        }
    }
    /**
   * 生成综合建议
   */ generateComprehensiveRecommendations(schedule, balanceAnalysis, postponedAlerts) {
        const recommendations = [];
        // 基于时间安排的建议
        if (schedule.timeSlots.length === 0) {
            recommendations.push('📅 今日暂无安排的任务，可以处理一些推迟的任务或进行个人提升');
        } else if (schedule.estimatedDuration > 480) {
            recommendations.push('⏰ 今日安排较满，注意劳逸结合，适当休息');
        }
        // 基于生活平衡的建议
        if (balanceAnalysis.balanceScore < 60) {
            recommendations.push('⚖️ 生活平衡需要调整，' + balanceAnalysis.recommendations[0]);
        }
        // 基于推迟任务的建议
        const urgentAlerts = postponedAlerts.filter((alert)=>alert.severity === 'high');
        if (urgentAlerts.length > 0) {
            recommendations.push(`🚨 有 ${urgentAlerts.length} 个高优先级推迟任务需要立即处理`);
        }
        // 基于任务分布的建议
        const categoryDistribution = this.analyzeCategoryDistribution(schedule);
        if (categoryDistribution.work > 0.8) {
            recommendations.push('💼 今日工作任务较多，记得安排适当的休息时间');
        } else if (categoryDistribution.entertainment > 0.4) {
            recommendations.push('🎮 娱乐时间较多，可以考虑增加一些学习或工作任务');
        }
        return recommendations;
    }
    /**
   * 分析任务分类分布
   */ analyzeCategoryDistribution(schedule) {
        const total = schedule.estimatedDuration;
        if (total === 0) return {
            work: 0,
            improvement: 0,
            entertainment: 0
        };
        const distribution = {
            work: 0,
            improvement: 0,
            entertainment: 0
        };
        for (const slot of schedule.timeSlots){
            const duration = (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60);
            distribution[slot.task.category] += duration;
        }
        return {
            work: distribution.work / total,
            improvement: distribution.improvement / total,
            entertainment: distribution.entertainment / total
        };
    }
    // ============================================================================
    // 算法性能监控
    // ============================================================================
    /**
   * 获取算法性能统计
   */ getAlgorithmStats() {
        // 这里可以添加性能监控逻辑
        return {
            planningCalls: 0,
            balanceCalls: 0,
            fixCalls: 0,
            adjustmentCalls: 0
        };
    }
    /**
   * 重置算法统计
   */ resetAlgorithmStats() {
        // 重置性能统计
        console.log('算法统计已重置');
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/intelligent-planning/services/PlanningService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 智能规划服务
 * 提供高级的规划服务接口，封装算法复杂性
 */ __turbopack_context__.s({
    "PlanningService": (()=>PlanningService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/coordinators/AlgorithmCoordinator.ts [app-client] (ecmascript)");
;
class PlanningService {
    algorithmCoordinator;
    constructor(){
        this.algorithmCoordinator = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlgorithmCoordinator"]();
    }
    // ============================================================================
    // 每日规划服务
    // ============================================================================
    /**
   * 生成智能每日规划
   */ async generateDailyPlan(userId, tasks, userTimeConfig) {
        try {
            // 验证输入
            if (!userId) {
                return {
                    success: false,
                    error: '用户ID不能为空',
                    data: null
                };
            }
            if (!Array.isArray(tasks)) {
                return {
                    success: false,
                    error: '任务列表格式不正确',
                    data: null
                };
            }
            // 生成每日洞察
            const insights = await this.algorithmCoordinator.generateDailyInsights(userId, tasks, userTimeConfig);
            return {
                success: true,
                data: {
                    schedule: insights.schedule,
                    insights: {
                        balanceAnalysis: insights.balanceAnalysis,
                        postponedAlerts: insights.postponedAlerts,
                        stats: this.algorithmCoordinator.getPostponedTasksStats(tasks)
                    },
                    recommendations: insights.recommendations
                }
            };
        } catch (error) {
            console.error('生成每日规划失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '生成每日规划时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 快速生成时间安排（简化版）
   */ async generateQuickSchedule(tasks, workHours) {
        try {
            const schedule = this.algorithmCoordinator.planningAlgorithm.generateTimeSlots(tasks.map((task)=>({
                    ...task,
                    score: 0,
                    quadrant: 1
                })), workHours || {
                start: '09:00',
                end: '18:00'
            });
            return {
                success: true,
                data: {
                    date: new Date(),
                    timeSlots: schedule,
                    totalTasks: tasks.length,
                    estimatedDuration: schedule.reduce((sum, slot)=>sum + (slot.endTime.getTime() - slot.startTime.getTime()) / (1000 * 60), 0)
                }
            };
        } catch (error) {
            console.error('生成快速安排失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '生成快速安排时发生错误',
                data: null
            };
        }
    }
    // ============================================================================
    // 生活平衡服务
    // ============================================================================
    /**
   * 获取生活平衡分析
   */ async getBalanceAnalysis(userId) {
        try {
            if (!userId) {
                return {
                    success: false,
                    error: '用户ID不能为空',
                    data: null
                };
            }
            const analysis = await this.algorithmCoordinator.analyzeLifeBalance(userId);
            return {
                success: true,
                data: analysis
            };
        } catch (error) {
            console.error('获取生活平衡分析失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取生活平衡分析时发生错误',
                data: null
            };
        }
    }
    /**
   * 更新活动统计
   */ async updateActivityStats(userId, category, duration) {
        try {
            if (!userId || !category || duration <= 0) {
                return {
                    success: false,
                    error: '参数不完整或无效',
                    data: undefined
                };
            }
            await this.algorithmCoordinator.updateTodayStats(userId, category, duration);
            return {
                success: true,
                data: undefined
            };
        } catch (error) {
            console.error('更新活动统计失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '更新活动统计时发生错误',
                data: undefined
            };
        }
    }
    // ============================================================================
    // 任务修复服务
    // ============================================================================
    /**
   * 获取推迟任务分析
   */ async getPostponedTasksAnalysis(tasks) {
        try {
            if (!Array.isArray(tasks)) {
                return {
                    success: false,
                    error: '任务列表格式不正确',
                    data: null
                };
            }
            const alerts = this.algorithmCoordinator.analyzePostponedTasks(tasks);
            const stats = this.algorithmCoordinator.getPostponedTasksStats(tasks);
            const actionPlan = this.algorithmCoordinator.generateActionPlan(alerts);
            return {
                success: true,
                data: {
                    alerts,
                    stats,
                    actionPlan
                }
            };
        } catch (error) {
            console.error('获取推迟任务分析失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取推迟任务分析时发生错误',
                data: null
            };
        }
    }
    /**
   * 获取任务重新安排建议
   */ async getTaskRescheduleAdvice(task) {
        try {
            if (!task || !task.id) {
                return {
                    success: false,
                    error: '任务信息不完整',
                    data: null
                };
            }
            const advice = this.algorithmCoordinator.suggestRescheduleStrategy(task);
            return {
                success: true,
                data: advice
            };
        } catch (error) {
            console.error('获取重新安排建议失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取重新安排建议时发生错误',
                data: null
            };
        }
    }
    // ============================================================================
    // 时间调整服务
    // ============================================================================
    /**
   * 处理任务超时
   */ async handleTaskOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig) {
        try {
            // 验证输入
            if (!overrunTask || !overrunTask.id) {
                return {
                    success: false,
                    error: '超时任务信息不完整',
                    data: null
                };
            }
            if (actualDuration <= 0) {
                return {
                    success: false,
                    error: '实际持续时间必须大于0',
                    data: null
                };
            }
            if (!Array.isArray(currentSchedule)) {
                return {
                    success: false,
                    error: '当前安排格式不正确',
                    data: null
                };
            }
            const result = await this.algorithmCoordinator.handleTaskOverrun(overrunTask, actualDuration, currentSchedule, userTimeConfig);
            return {
                success: true,
                data: result
            };
        } catch (error) {
            console.error('处理任务超时失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '处理任务超时时发生错误',
                data: null
            };
        }
    }
    /**
   * 预测时间调整影响
   */ async predictOverrunImpact(overrunMinutes, schedule, overrunTaskIndex) {
        try {
            if (overrunMinutes <= 0) {
                return {
                    success: false,
                    error: '超时分钟数必须大于0',
                    data: null
                };
            }
            if (!Array.isArray(schedule) || overrunTaskIndex < 0 || overrunTaskIndex >= schedule.length) {
                return {
                    success: false,
                    error: '安排信息或任务索引无效',
                    data: null
                };
            }
            const impact = this.algorithmCoordinator.predictAdjustmentImpact(overrunMinutes, schedule, overrunTaskIndex);
            return {
                success: true,
                data: impact
            };
        } catch (error) {
            console.error('预测调整影响失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '预测调整影响时发生错误',
                data: null
            };
        }
    }
    // ============================================================================
    // 工具方法
    // ============================================================================
    /**
   * 获取任务建议
   */ getTaskRecommendation(task) {
        try {
            return this.algorithmCoordinator.getTaskRecommendation(task);
        } catch (error) {
            console.error('获取任务建议失败:', error);
            return '暂无建议';
        }
    }
    /**
   * 获取分类时间建议
   */ getCategoryAdvice(category, ratio) {
        try {
            return this.algorithmCoordinator.getCategoryTimeRecommendation(category, ratio);
        } catch (error) {
            console.error('获取分类建议失败:', error);
            return '暂无建议';
        }
    }
    /**
   * 获取服务状态
   */ getServiceStatus() {
        try {
            return {
                isHealthy: true,
                algorithmStats: this.algorithmCoordinator.getAlgorithmStats(),
                lastUpdate: new Date()
            };
        } catch (error) {
            console.error('获取服务状态失败:', error);
            return {
                isHealthy: false,
                algorithmStats: null,
                lastUpdate: new Date()
            };
        }
    }
    /**
   * 重置服务统计
   */ resetServiceStats() {
        try {
            this.algorithmCoordinator.resetAlgorithmStats();
        } catch (error) {
            console.error('重置服务统计失败:', error);
        }
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/intelligent-planning/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 智能规划域统一导出
 * 提供智能规划相关的所有功能
 */ // ============================================================================
// 算法导出
// ============================================================================
__turbopack_context__.s({
    "createAlgorithmCoordinator": (()=>createAlgorithmCoordinator),
    "createBalanceAlgorithm": (()=>createBalanceAlgorithm),
    "createFixAlgorithm": (()=>createFixAlgorithm),
    "createPlanningAlgorithm": (()=>createPlanningAlgorithm),
    "createPlanningService": (()=>createPlanningService),
    "createTimeAdjustmentAlgorithm": (()=>createTimeAdjustmentAlgorithm),
    "getDefaultPlanningService": (()=>getDefaultPlanningService),
    "getPlanningDomainFeatures": (()=>getPlanningDomainFeatures),
    "resetDefaultPlanningService": (()=>resetDefaultPlanningService),
    "runAlgorithmBenchmark": (()=>runAlgorithmBenchmark),
    "validatePlanningServiceHealth": (()=>validatePlanningServiceHealth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/PlanningAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/BalanceAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/FixAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/TimeAdjustmentAlgorithm.ts [app-client] (ecmascript)");
// ============================================================================
// 协调器导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/coordinators/AlgorithmCoordinator.ts [app-client] (ecmascript)");
// ============================================================================
// 服务导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$services$2f$PlanningService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/services/PlanningService.ts [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
function createPlanningService() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$services$2f$PlanningService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlanningService"]();
}
function createAlgorithmCoordinator() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlgorithmCoordinator"]();
}
function createPlanningAlgorithm() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlanningAlgorithm"]();
}
function createBalanceAlgorithm() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BalanceAlgorithm"]();
}
function createFixAlgorithm() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FixAlgorithm"]();
}
function createTimeAdjustmentAlgorithm() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TimeAdjustmentAlgorithm"]();
}
// ============================================================================
// 默认实例（单例模式）
// ============================================================================
let defaultPlanningService = null;
function getDefaultPlanningService() {
    if (!defaultPlanningService) {
        defaultPlanningService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$services$2f$PlanningService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlanningService"]();
    }
    return defaultPlanningService;
}
function resetDefaultPlanningService() {
    defaultPlanningService = null;
}
async function validatePlanningServiceHealth() {
    const errors = [];
    let isHealthy = true;
    try {
        const service = getDefaultPlanningService();
        const status = service.getServiceStatus();
        if (!status.isHealthy) {
            errors.push('规划服务状态异常');
            isHealthy = false;
        }
    } catch (error) {
        errors.push(`规划服务初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
        isHealthy = false;
    }
    return {
        isHealthy,
        errors,
        timestamp: new Date()
    };
}
function getPlanningDomainFeatures() {
    return {
        algorithms: [
            'PlanningAlgorithm - 智能时间规划',
            'BalanceAlgorithm - 生活平衡分析',
            'FixAlgorithm - 推迟任务修复',
            'TimeAdjustmentAlgorithm - 动态时间调整'
        ],
        services: [
            'PlanningService - 统一规划服务接口',
            'AlgorithmCoordinator - 算法协调管理'
        ],
        capabilities: [
            '基于四象限的智能任务排序',
            '考虑任务类型的时间分配',
            '生活平衡状况分析和建议',
            '推迟任务检测和修复建议',
            '任务超时的动态时间调整',
            '综合的每日规划洞察',
            '个性化的时间管理建议'
        ]
    };
}
async function runAlgorithmBenchmark() {
    const coordinator = createAlgorithmCoordinator();
    // 模拟测试数据
    const mockTasks = [
        {
            id: '1',
            userId: 'test',
            title: '测试任务1',
            category: 'work',
            importance: 4,
            urgency: 3,
            deadline: new Date(),
            estimatedDuration: 60,
            status: 'pending',
            postponeCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        }
    ];
    const startTime = Date.now();
    // 测试规划算法
    const planningStart = Date.now();
    await coordinator.generateDailySchedule(mockTasks);
    const planningTime = Date.now() - planningStart;
    // 测试平衡算法
    const balanceStart = Date.now();
    await coordinator.analyzeLifeBalance('test');
    const balanceTime = Date.now() - balanceStart;
    // 测试修复算法
    const fixStart = Date.now();
    coordinator.analyzePostponedTasks(mockTasks);
    const fixTime = Date.now() - fixStart;
    // 测试调整算法
    const adjustmentStart = Date.now();
    coordinator.predictAdjustmentImpact(30, [], 0);
    const adjustmentTime = Date.now() - adjustmentStart;
    const totalTime = Date.now() - startTime;
    return {
        planningTime,
        balanceTime,
        fixTime,
        adjustmentTime,
        totalTime
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/intelligent-planning/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$PlanningAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/PlanningAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$BalanceAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/BalanceAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$FixAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/FixAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$algorithms$2f$TimeAdjustmentAlgorithm$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/algorithms/TimeAdjustmentAlgorithm.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$coordinators$2f$AlgorithmCoordinator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/coordinators/AlgorithmCoordinator.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$services$2f$PlanningService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/services/PlanningService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/domains/task-management/models/Task.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 任务模型
 * 定义任务的数据结构和相关类型
 */ __turbopack_context__.s({
    "mapRecordToTask": (()=>mapRecordToTask),
    "mapTaskToRecord": (()=>mapTaskToRecord)
});
function mapRecordToTask(record) {
    return {
        id: record.id,
        userId: record.user_id,
        title: record.title,
        description: record.description,
        category: record.category,
        importance: record.importance,
        urgency: record.urgency,
        deadline: new Date(record.deadline),
        estimatedDuration: record.estimated_duration,
        status: record.status,
        postponeCount: record.postpone_count,
        createdAt: new Date(record.created_at),
        updatedAt: new Date(record.updated_at)
    };
}
function mapTaskToRecord(task) {
    return {
        user_id: task.userId,
        title: task.title,
        description: task.description || '',
        category: task.category,
        importance: task.importance,
        urgency: task.urgency,
        deadline: task.deadline.toISOString(),
        estimated_duration: task.estimatedDuration,
        status: task.status || 'pending',
        postpone_count: task.postponeCount || 0
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/task-management/repositories/TaskRepository.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 任务仓储层
 * 负责任务数据的持久化操作
 */ __turbopack_context__.s({
    "TaskRepository": (()=>TaskRepository)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/models/Task.ts [app-client] (ecmascript)");
;
;
class TaskRepository {
    /**
   * 获取用户的所有任务
   */ async findByUserId(userId) {
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*').eq('user_id', userId).order('created_at', {
                ascending: false
            });
            if (error) {
                return {
                    success: false,
                    error: `获取任务失败: ${error.message}`,
                    data: []
                };
            }
            const tasks = data.map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mapRecordToTask"]);
            return {
                success: true,
                data: tasks
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取任务时发生未知错误',
                data: []
            };
        }
    }
    /**
   * 根据ID获取任务
   */ async findById(id) {
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*').eq('id', id).single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return {
                        success: true,
                        data: null
                    };
                }
                return {
                    success: false,
                    error: `获取任务失败: ${error.message}`,
                    data: null
                };
            }
            const task = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mapRecordToTask"])(data);
            return {
                success: true,
                data: task
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '获取任务时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 创建新任务
   */ async create(taskData) {
        try {
            const record = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mapTaskToRecord"])(taskData);
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').insert(record).select().single();
            if (error) {
                return {
                    success: false,
                    error: `创建任务失败: ${error.message}`,
                    data: null
                };
            }
            const task = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mapRecordToTask"])(data);
            return {
                success: true,
                data: task
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '创建任务时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 更新任务
   */ async update(updateData) {
        try {
            const { id, ...updates } = updateData;
            // 构建更新数据
            const updateRecord = {};
            if (updates.title !== undefined) updateRecord.title = updates.title;
            if (updates.description !== undefined) updateRecord.description = updates.description;
            if (updates.category !== undefined) updateRecord.category = updates.category;
            if (updates.importance !== undefined) updateRecord.importance = updates.importance;
            if (updates.urgency !== undefined) updateRecord.urgency = updates.urgency;
            if (updates.deadline !== undefined) updateRecord.deadline = updates.deadline.toISOString();
            if (updates.estimatedDuration !== undefined) updateRecord.estimated_duration = updates.estimatedDuration;
            if (updates.status !== undefined) updateRecord.status = updates.status;
            if (updates.postponeCount !== undefined) updateRecord.postpone_count = updates.postponeCount;
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').update(updateRecord).eq('id', id).select().single();
            if (error) {
                return {
                    success: false,
                    error: `更新任务失败: ${error.message}`,
                    data: null
                };
            }
            const task = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mapRecordToTask"])(data);
            return {
                success: true,
                data: task
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '更新任务时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 删除任务
   */ async delete(id) {
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').delete().eq('id', id);
            if (error) {
                return {
                    success: false,
                    error: `删除任务失败: ${error.message}`,
                    data: undefined
                };
            }
            return {
                success: true,
                data: undefined
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '删除任务时发生未知错误',
                data: undefined
            };
        }
    }
    /**
   * 根据条件过滤任务
   */ async findByFilter(filter) {
        try {
            let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*').eq('user_id', filter.userId);
            // 应用状态过滤
            if (filter.status) {
                if (Array.isArray(filter.status)) {
                    query = query.in('status', filter.status);
                } else {
                    query = query.eq('status', filter.status);
                }
            }
            // 应用分类过滤
            if (filter.category) {
                if (Array.isArray(filter.category)) {
                    query = query.in('category', filter.category);
                } else {
                    query = query.eq('category', filter.category);
                }
            }
            // 应用日期范围过滤
            if (filter.startDate) {
                query = query.gte('deadline', filter.startDate.toISOString());
            }
            if (filter.endDate) {
                query = query.lte('deadline', filter.endDate.toISOString());
            }
            // 应用搜索条件
            if (filter.searchTerm) {
                query = query.or(`title.ilike.%${filter.searchTerm}%,description.ilike.%${filter.searchTerm}%`);
            }
            const { data, error } = await query.order('created_at', {
                ascending: false
            });
            if (error) {
                return {
                    success: false,
                    error: `过滤任务失败: ${error.message}`,
                    data: []
                };
            }
            const tasks = data.map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mapRecordToTask"]);
            return {
                success: true,
                data: tasks
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '过滤任务时发生未知错误',
                data: []
            };
        }
    }
    /**
   * 分页获取任务
   */ async findWithPagination(userId, pagination, sort) {
        try {
            const { page, pageSize } = pagination;
            const offset = (page - 1) * pageSize;
            let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').select('*', {
                count: 'exact'
            }).eq('user_id', userId);
            // 应用排序
            if (sort) {
                const column = this.mapSortOptionToColumn(sort.sortBy);
                query = query.order(column, {
                    ascending: sort.direction === 'asc'
                });
            } else {
                query = query.order('created_at', {
                    ascending: false
                });
            }
            // 应用分页
            query = query.range(offset, offset + pageSize - 1);
            const { data, error, count } = await query;
            if (error) {
                return {
                    success: false,
                    error: `分页获取任务失败: ${error.message}`,
                    data: null
                };
            }
            const tasks = data.map(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mapRecordToTask"]);
            const total = count || 0;
            const totalPages = Math.ceil(total / pageSize);
            return {
                success: true,
                data: {
                    tasks,
                    total,
                    page,
                    pageSize,
                    totalPages
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '分页获取任务时发生未知错误',
                data: null
            };
        }
    }
    /**
   * 批量更新任务状态
   */ async batchUpdateStatus(taskIds, status) {
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('tasks').update({
                status
            }).in('id', taskIds);
            if (error) {
                return {
                    success: false,
                    error: `批量更新任务状态失败: ${error.message}`,
                    data: undefined
                };
            }
            return {
                success: true,
                data: undefined
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '批量更新任务状态时发生未知错误',
                data: undefined
            };
        }
    }
    /**
   * 映射排序选项到数据库列名
   */ mapSortOptionToColumn(sortOption) {
        const mapping = {
            deadline: 'deadline',
            importance: 'importance',
            urgency: 'urgency',
            createdAt: 'created_at',
            estimatedDuration: 'estimated_duration'
        };
        return mapping[sortOption] || 'created_at';
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/task-management/services/TaskValidationService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 任务验证服务
 * 负责任务数据的验证和业务规则检查
 */ __turbopack_context__.s({
    "TaskValidationService": (()=>TaskValidationService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/constants/index.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-client] (ecmascript) <locals>");
;
class TaskValidationService {
    /**
   * 验证任务创建请求
   */ validateCreateRequest(request) {
        const errors = [];
        const warnings = [];
        // 验证必填字段
        if (!request.userId || request.userId.trim().length === 0) {
            errors.push('用户ID不能为空');
        }
        if (!request.title || request.title.trim().length === 0) {
            errors.push('任务标题不能为空');
        } else if (request.title.length > 100) {
            errors.push('任务标题不能超过100个字符');
        }
        // 验证分类
        if (!request.category || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidTaskCategory"])(request.category)) {
            errors.push('请选择有效的任务分类');
        }
        // 验证重要性
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidPriority"])(request.importance)) {
            errors.push('重要性必须在1-5之间');
        }
        // 验证紧急性
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidPriority"])(request.urgency)) {
            errors.push('紧急性必须在1-5之间');
        }
        // 验证截止时间
        if (!request.deadline) {
            errors.push('请设置截止时间');
        } else {
            const now = new Date();
            if (request.deadline < now) {
                errors.push('截止时间不能早于当前时间');
            }
            // 警告：截止时间过于遥远
            const oneYearLater = new Date();
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
            if (request.deadline > oneYearLater) {
                warnings.push('截止时间设置得较远，建议设置更近的时间以保持紧迫感');
            }
        }
        // 验证预估时长
        if (!request.estimatedDuration || request.estimatedDuration <= 0) {
            errors.push('预估时长必须大于0');
        } else if (request.estimatedDuration < 15) {
            warnings.push('预估时长少于15分钟，建议合并到其他任务中');
        } else if (request.estimatedDuration > 480) {
            warnings.push('预估时长超过8小时，建议分解为更小的任务');
        }
        // 验证描述长度
        if (request.description && request.description.length > 500) {
            errors.push('任务描述不能超过500个字符');
        }
        // 业务规则验证
        this.validateBusinessRules(request, errors, warnings);
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
   * 验证任务更新请求
   */ validateUpdateRequest(request) {
        const errors = [];
        const warnings = [];
        // 验证ID
        if (!request.id || request.id.trim().length === 0) {
            errors.push('任务ID不能为空');
        }
        // 验证标题（如果提供）
        if (request.title !== undefined) {
            if (request.title.trim().length === 0) {
                errors.push('任务标题不能为空');
            } else if (request.title.length > 100) {
                errors.push('任务标题不能超过100个字符');
            }
        }
        // 验证分类（如果提供）
        if (request.category !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidTaskCategory"])(request.category)) {
            errors.push('请选择有效的任务分类');
        }
        // 验证重要性（如果提供）
        if (request.importance !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidPriority"])(request.importance)) {
            errors.push('重要性必须在1-5之间');
        }
        // 验证紧急性（如果提供）
        if (request.urgency !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidPriority"])(request.urgency)) {
            errors.push('紧急性必须在1-5之间');
        }
        // 验证截止时间（如果提供）
        if (request.deadline !== undefined) {
            const now = new Date();
            if (request.deadline < now) {
                errors.push('截止时间不能早于当前时间');
            }
        }
        // 验证预估时长（如果提供）
        if (request.estimatedDuration !== undefined) {
            if (request.estimatedDuration <= 0) {
                errors.push('预估时长必须大于0');
            } else if (request.estimatedDuration > 480) {
                warnings.push('预估时长超过8小时，建议分解为更小的任务');
            }
        }
        // 验证状态（如果提供）
        if (request.status !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isValidTaskStatus"])(request.status)) {
            errors.push('请选择有效的任务状态');
        }
        // 验证描述长度（如果提供）
        if (request.description !== undefined && request.description.length > 500) {
            errors.push('任务描述不能超过500个字符');
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
   * 验证任务完成请求
   */ validateCompleteRequest(request, task) {
        const errors = [];
        const warnings = [];
        // 验证任务状态
        if (task.status === 'completed') {
            errors.push('任务已经完成');
        }
        // 验证实际时长
        if (request.actualDuration !== undefined) {
            if (request.actualDuration <= 0) {
                errors.push('实际时长必须大于0');
            } else {
                // 检查实际时长与预估时长的差异
                const estimatedDuration = task.estimatedDuration;
                const ratio = request.actualDuration / estimatedDuration;
                if (ratio > 2) {
                    warnings.push('实际时长远超预估时长，建议调整未来类似任务的时间预估');
                } else if (ratio < 0.5) {
                    warnings.push('实际时长远少于预估时长，建议调整未来类似任务的时间预估');
                }
            }
        }
        // 验证满意度评分
        if (request.satisfactionScore !== undefined) {
            if (request.satisfactionScore < 1 || request.satisfactionScore > 5) {
                errors.push('满意度评分必须在1-5之间');
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
   * 验证任务推迟请求
   */ validatePostponeRequest(request, task) {
        const errors = [];
        const warnings = [];
        // 验证任务状态
        if (task.status === 'completed') {
            errors.push('已完成的任务不能推迟');
        }
        // 验证推迟次数
        if (task.postponeCount >= 3) {
            warnings.push('该任务已推迟多次，建议重新评估任务的必要性或分解任务');
        }
        // 验证新的截止时间
        if (request.newDeadline) {
            const now = new Date();
            if (request.newDeadline <= now) {
                errors.push('新的截止时间必须晚于当前时间');
            }
            if (request.newDeadline <= task.deadline) {
                errors.push('新的截止时间必须晚于原截止时间');
            }
        }
        // 验证推迟原因
        if (request.reason && request.reason.length > 200) {
            errors.push('推迟原因不能超过200个字符');
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
   * 验证业务规则
   */ validateBusinessRules(request, errors, warnings) {
        // 规则1：娱乐任务不应该设置过高的重要性和紧急性
        if (request.category === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].ENTERTAINMENT) {
            if (request.importance >= 4 && request.urgency >= 4) {
                warnings.push('娱乐任务通常不需要设置过高的重要性和紧急性');
            }
        }
        // 规则2：工作任务在非工作时间的警告
        if (request.category === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TASK_CATEGORIES"].WORK) {
            const deadlineHour = request.deadline.getHours();
            if (deadlineHour < 9 || deadlineHour > 18) {
                warnings.push('工作任务的截止时间设置在非工作时间，请确认是否合理');
            }
        }
        // 规则3：高重要性但低紧急性的任务应该有合理的时间安排
        if (request.importance >= 4 && request.urgency <= 2) {
            const timeToDeadline = request.deadline.getTime() - new Date().getTime();
            const daysToDeadline = timeToDeadline / (1000 * 60 * 60 * 24);
            if (daysToDeadline < 1) {
                warnings.push('重要但不紧急的任务截止时间过近，可能影响执行质量');
            }
        }
        // 规则4：短时间任务的合理性检查
        if (request.estimatedDuration < 30) {
            const timeToDeadline = request.deadline.getTime() - new Date().getTime();
            const hoursToDeadline = timeToDeadline / (1000 * 60 * 60);
            if (hoursToDeadline > 24 && request.urgency >= 4) {
                warnings.push('短时间任务设置了较远的截止时间但标记为紧急，请检查设置是否合理');
            }
        }
    }
    /**
   * 验证任务状态转换的合法性
   */ validateStatusTransition(currentStatus, newStatus) {
        const errors = [];
        const warnings = [];
        // 定义合法的状态转换
        const validTransitions = {
            'pending': [
                'in-progress',
                'postponed',
                'completed'
            ],
            'in-progress': [
                'completed',
                'postponed',
                'pending'
            ],
            'completed': [],
            'postponed': [
                'pending',
                'in-progress',
                'completed'
            ]
        };
        if (!validTransitions[currentStatus].includes(newStatus)) {
            errors.push(`不能从状态 "${currentStatus}" 转换到 "${newStatus}"`);
        }
        // 特殊情况的警告
        if (currentStatus === 'completed' && newStatus !== 'completed') {
            warnings.push('重新激活已完成的任务，请确认是否必要');
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/task-management/services/TaskService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 任务服务
 * 提供任务管理的核心业务逻辑
 */ __turbopack_context__.s({
    "TaskService": (()=>TaskService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/shared/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/utils/taskUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$repositories$2f$TaskRepository$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/repositories/TaskRepository.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskValidationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskValidationService.ts [app-client] (ecmascript)");
;
;
;
class TaskService {
    repository;
    validationService;
    constructor(){
        this.repository = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$repositories$2f$TaskRepository$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskRepository"]();
        this.validationService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskValidationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskValidationService"]();
    }
    // ============================================================================
    // 基础 CRUD 操作
    // ============================================================================
    /**
   * 获取用户的所有任务
   */ async getUserTasks(userId) {
        if (!userId) {
            return {
                success: false,
                error: '用户ID不能为空',
                data: []
            };
        }
        return await this.repository.findByUserId(userId);
    }
    /**
   * 根据ID获取任务
   */ async getTaskById(id) {
        if (!id) {
            return {
                success: false,
                error: '任务ID不能为空',
                data: null
            };
        }
        return await this.repository.findById(id);
    }
    /**
   * 创建新任务
   */ async createTask(request) {
        // 验证请求数据
        const validation = this.validationService.validateCreateRequest(request);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.errors.join('; '),
                data: null
            };
        }
        // 创建任务
        const result = await this.repository.create(request);
        if (result.success && validation.warnings.length > 0) {
            console.warn('任务创建警告:', validation.warnings);
        }
        return result;
    }
    /**
   * 更新任务
   */ async updateTask(request) {
        // 验证请求数据
        const validation = this.validationService.validateUpdateRequest(request);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.errors.join('; '),
                data: null
            };
        }
        // 如果更新状态，验证状态转换
        if (request.status) {
            const currentTaskResult = await this.repository.findById(request.id);
            if (!currentTaskResult.success || !currentTaskResult.data) {
                return {
                    success: false,
                    error: '任务不存在',
                    data: null
                };
            }
            const statusValidation = this.validationService.validateStatusTransition(currentTaskResult.data.status, request.status);
            if (!statusValidation.isValid) {
                return {
                    success: false,
                    error: statusValidation.errors.join('; '),
                    data: null
                };
            }
        }
        return await this.repository.update(request);
    }
    /**
   * 删除任务
   */ async deleteTask(id) {
        if (!id) {
            return {
                success: false,
                error: '任务ID不能为空',
                data: undefined
            };
        }
        // 检查任务是否存在
        const taskResult = await this.repository.findById(id);
        if (!taskResult.success || !taskResult.data) {
            return {
                success: false,
                error: '任务不存在',
                data: undefined
            };
        }
        return await this.repository.delete(id);
    }
    // ============================================================================
    // 任务状态管理
    // ============================================================================
    /**
   * 完成任务
   */ async completeTask(request) {
        // 获取当前任务
        const taskResult = await this.repository.findById(request.id);
        if (!taskResult.success || !taskResult.data) {
            return {
                success: false,
                error: '任务不存在',
                data: null
            };
        }
        const task = taskResult.data;
        // 验证完成请求
        const validation = this.validationService.validateCompleteRequest(request, task);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.errors.join('; '),
                data: null
            };
        }
        // 更新任务状态
        const updateResult = await this.repository.update({
            id: request.id,
            status: 'completed'
        });
        if (updateResult.success && validation.warnings.length > 0) {
            console.warn('任务完成警告:', validation.warnings);
        }
        return updateResult;
    }
    /**
   * 推迟任务
   */ async postponeTask(request) {
        // 获取当前任务
        const taskResult = await this.repository.findById(request.id);
        if (!taskResult.success || !taskResult.data) {
            return {
                success: false,
                error: '任务不存在',
                data: null
            };
        }
        const task = taskResult.data;
        // 验证推迟请求
        const validation = this.validationService.validatePostponeRequest(request, task);
        if (!validation.isValid) {
            return {
                success: false,
                error: validation.errors.join('; '),
                data: null
            };
        }
        // 更新任务
        const updateData = {
            id: request.id,
            status: 'postponed',
            postponeCount: task.postponeCount + 1
        };
        if (request.newDeadline) {
            updateData.deadline = request.newDeadline;
        }
        const updateResult = await this.repository.update(updateData);
        if (updateResult.success && validation.warnings.length > 0) {
            console.warn('任务推迟警告:', validation.warnings);
        }
        return updateResult;
    }
    /**
   * 开始任务
   */ async startTask(id) {
        return await this.updateTask({
            id,
            status: 'in-progress'
        });
    }
    /**
   * 暂停任务
   */ async pauseTask(id) {
        return await this.updateTask({
            id,
            status: 'pending'
        });
    }
    // ============================================================================
    // 任务查询和过滤
    // ============================================================================
    /**
   * 根据条件过滤任务
   */ async getTasksByFilter(filter) {
        return await this.repository.findByFilter(filter);
    }
    /**
   * 分页获取任务
   */ async getTasksWithPagination(userId, pagination, sort) {
        if (!userId) {
            return {
                success: false,
                error: '用户ID不能为空',
                data: null
            };
        }
        return await this.repository.findWithPagination(userId, pagination, sort);
    }
    /**
   * 获取今日任务
   */ async getTodayTasks(userId) {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const filter = {
            userId,
            status: [
                'pending',
                'in-progress'
            ],
            endDate: tomorrow
        };
        return await this.getTasksByFilter(filter);
    }
    /**
   * 获取过期任务
   */ async getOverdueTasks(userId) {
        const tasksResult = await this.getUserTasks(userId);
        if (!tasksResult.success) {
            return tasksResult;
        }
        const overdueTasks = tasksResult.data.filter((task)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task) && task.status !== 'completed');
        return {
            success: true,
            data: overdueTasks
        };
    }
    /**
   * 获取即将到期的任务
   */ async getDueSoonTasks(userId, hoursThreshold = 24) {
        const tasksResult = await this.getUserTasks(userId);
        if (!tasksResult.success) {
            return tasksResult;
        }
        const dueSoonTasks = tasksResult.data.filter((task)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTaskDueSoon"])(task, hoursThreshold) && task.status !== 'completed');
        return {
            success: true,
            data: dueSoonTasks
        };
    }
    // ============================================================================
    // 任务统计
    // ============================================================================
    /**
   * 获取任务统计信息
   */ async getTaskStatistics(userId) {
        const tasksResult = await this.getUserTasks(userId);
        if (!tasksResult.success) {
            return {
                success: false,
                error: tasksResult.error,
                data: null
            };
        }
        const tasks = tasksResult.data;
        // 按状态统计
        const byStatus = tasks.reduce((acc, task)=>{
            acc[task.status] = (acc[task.status] || 0) + 1;
            return acc;
        }, {});
        // 按分类统计
        const byCategory = tasks.reduce((acc, task)=>{
            acc[task.category] = (acc[task.category] || 0) + 1;
            return acc;
        }, {});
        // 按象限统计
        const byQuadrant = tasks.reduce((acc, task)=>{
            const quadrant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["classifyQuadrant"])(task.importance, task.urgency);
            acc[quadrant] = (acc[quadrant] || 0) + 1;
            return acc;
        }, {});
        // 过期和即将到期任务数量
        const overdue = tasks.filter((task)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTaskOverdue"])(task) && task.status !== 'completed').length;
        const dueSoon = tasks.filter((task)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$utils$2f$taskUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTaskDueSoon"])(task) && task.status !== 'completed').length;
        // 平均完成时间（这里简化处理，实际应该从完成记录中计算）
        const completedTasks = tasks.filter((task)=>task.status === 'completed');
        const averageCompletionTime = completedTasks.length > 0 ? completedTasks.reduce((sum, task)=>sum + task.estimatedDuration, 0) / completedTasks.length : 0;
        const statistics = {
            total: tasks.length,
            byStatus: byStatus,
            byCategory: byCategory,
            byQuadrant: byQuadrant,
            overdue,
            dueSoon,
            averageCompletionTime
        };
        return {
            success: true,
            data: statistics
        };
    }
    // ============================================================================
    // 批量操作
    // ============================================================================
    /**
   * 批量更新任务状态
   */ async batchUpdateStatus(taskIds, status) {
        if (!taskIds || taskIds.length === 0) {
            return {
                success: false,
                error: '任务ID列表不能为空',
                data: undefined
            };
        }
        return await this.repository.batchUpdateStatus(taskIds, status);
    }
    /**
   * 批量删除任务
   */ async batchDeleteTasks(taskIds) {
        if (!taskIds || taskIds.length === 0) {
            return {
                success: false,
                error: '任务ID列表不能为空',
                data: undefined
            };
        }
        try {
            for (const id of taskIds){
                const result = await this.repository.delete(id);
                if (!result.success) {
                    return {
                        success: false,
                        error: `删除任务 ${id} 失败: ${result.error}`,
                        data: undefined
                    };
                }
            }
            return {
                success: true,
                data: undefined
            };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : '批量删除任务时发生未知错误',
                data: undefined
            };
        }
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/task-management/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 任务管理域统一导出
 * 提供任务管理相关的所有功能
 */ // ============================================================================
// 模型导出
// ============================================================================
__turbopack_context__.s({
    "createSampleTaskData": (()=>createSampleTaskData),
    "createTaskRepository": (()=>createTaskRepository),
    "createTaskService": (()=>createTaskService),
    "createTaskValidationService": (()=>createTaskValidationService),
    "getDefaultTaskService": (()=>getDefaultTaskService),
    "getTaskManagementConfig": (()=>getTaskManagementConfig),
    "getTaskManagementDomainFeatures": (()=>getTaskManagementDomainFeatures),
    "resetDefaultTaskService": (()=>resetDefaultTaskService),
    "runTaskManagementBenchmark": (()=>runTaskManagementBenchmark),
    "validateTaskServiceHealth": (()=>validateTaskServiceHealth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/models/Task.ts [app-client] (ecmascript)");
// ============================================================================
// 仓储导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$repositories$2f$TaskRepository$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/repositories/TaskRepository.ts [app-client] (ecmascript)");
// ============================================================================
// 服务导出
// ============================================================================
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskValidationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskValidationService.ts [app-client] (ecmascript)");
;
;
;
;
function createTaskService() {
    return new TaskService();
}
function createTaskRepository() {
    return new TaskRepository();
}
function createTaskValidationService() {
    return new TaskValidationService();
}
// ============================================================================
// 默认实例（单例模式）
// ============================================================================
let defaultTaskService = null;
function getDefaultTaskService() {
    if (!defaultTaskService) {
        defaultTaskService = new TaskService();
    }
    return defaultTaskService;
}
function resetDefaultTaskService() {
    defaultTaskService = null;
}
async function validateTaskServiceHealth() {
    const errors = [];
    let isHealthy = true;
    try {
        const service = getDefaultTaskService();
        // 尝试创建一个测试验证
        const testRequest = {
            userId: 'test',
            title: 'Test Task',
            category: 'work',
            importance: 3,
            urgency: 3,
            deadline: new Date(),
            estimatedDuration: 60
        };
        const validation = service['validationService'].validateCreateRequest(testRequest);
        if (!validation) {
            errors.push('任务验证服务不可用');
            isHealthy = false;
        }
    } catch (error) {
        errors.push(`任务服务初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
        isHealthy = false;
    }
    return {
        isHealthy,
        errors,
        timestamp: new Date()
    };
}
function getTaskManagementDomainFeatures() {
    return {
        models: [
            'Task - 核心任务模型',
            'TaskRecord - 数据库记录映射',
            'CreateTaskRequest - 任务创建请求',
            'UpdateTaskRequest - 任务更新请求',
            'CompleteTaskRequest - 任务完成请求',
            'PostponeTaskRequest - 任务推迟请求',
            'TaskFilter - 任务过滤条件',
            'TaskStatistics - 任务统计信息'
        ],
        repositories: [
            'TaskRepository - 任务数据持久化'
        ],
        services: [
            'TaskService - 核心任务业务逻辑',
            'TaskValidationService - 任务数据验证'
        ],
        capabilities: [
            '完整的任务 CRUD 操作',
            '任务状态生命周期管理',
            '任务数据验证和业务规则检查',
            '任务过滤、排序和分页',
            '任务统计和分析',
            '批量任务操作',
            '任务完成和推迟处理',
            '过期和即将到期任务检测',
            '任务状态转换验证',
            '业务规则和警告提示'
        ]
    };
}
async function runTaskManagementBenchmark() {
    const service = createTaskService();
    const validationService = createTaskValidationService();
    // 模拟测试数据
    const testRequest = {
        userId: 'benchmark-test',
        title: '性能测试任务',
        category: 'work',
        importance: 3,
        urgency: 3,
        deadline: new Date(),
        estimatedDuration: 60
    };
    const startTime = Date.now();
    // 测试验证性能
    const validationStart = Date.now();
    validationService.validateCreateRequest(testRequest);
    const validationTime = Date.now() - validationStart;
    // 注意：这里只测试验证逻辑，不执行实际的数据库操作
    // 实际的 CRUD 操作需要数据库连接
    const createTime = 0; // 模拟值
    const readTime = 0; // 模拟值
    const updateTime = 0; // 模拟值
    const deleteTime = 0; // 模拟值
    const totalTime = Date.now() - startTime;
    return {
        createTime,
        readTime,
        updateTime,
        deleteTime,
        validationTime,
        totalTime
    };
}
function getTaskManagementConfig() {
    return {
        maxTitleLength: 100,
        maxDescriptionLength: 500,
        maxPostponeCount: 5,
        defaultPageSize: 20,
        maxPageSize: 100
    };
}
function createSampleTaskData() {
    return [
        {
            userId: 'sample-user',
            title: '完成项目报告',
            description: '撰写季度项目总结报告',
            category: 'work',
            importance: 4,
            urgency: 3,
            deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            estimatedDuration: 120
        },
        {
            userId: 'sample-user',
            title: '学习新技术',
            description: '学习 React 18 的新特性',
            category: 'improvement',
            importance: 3,
            urgency: 2,
            deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
            estimatedDuration: 180
        },
        {
            userId: 'sample-user',
            title: '看电影放松',
            description: '观看最新上映的电影',
            category: 'entertainment',
            importance: 2,
            urgency: 1,
            deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
            estimatedDuration: 120
        }
    ];
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/domains/task-management/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$models$2f$Task$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/models/Task.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$repositories$2f$TaskRepository$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/repositories/TaskRepository.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$services$2f$TaskValidationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/domains/task-management/services/TaskValidationService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/domains/task-management/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/store/useTaskStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTaskStore": (()=>useTaskStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/domains/intelligent-planning/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/domains/task-management/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/domains/task-management/index.ts [app-client] (ecmascript) <locals>");
;
;
;
;
const useTaskStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])((set, get)=>({
        // Initial state
        tasks: [],
        dailySchedule: null,
        balanceAnalysis: null,
        postponedAlerts: [],
        loading: false,
        error: null,
        // Service instances
        planningService: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$intelligent$2d$planning$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getDefaultPlanningService"])(),
        taskService: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$domains$2f$task$2d$management$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getDefaultTaskService"])(),
        // Fetch tasks
        fetchTasks: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { taskService } = get();
                const result = await taskService.getUserTasks(userId);
                if (result.success) {
                    set({
                        tasks: result.data,
                        loading: false
                    });
                } else {
                    set({
                        error: result.error || 'Failed to fetch tasks',
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Create task
        createTask: async (taskData)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { taskService } = get();
                const result = await taskService.createTask(taskData);
                if (result.success) {
                    set((state)=>({
                            tasks: [
                                result.data,
                                ...state.tasks
                            ],
                            loading: false
                        }));
                } else {
                    set({
                        error: result.error || 'Failed to create task',
                        loading: false
                    });
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to create task';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        // Update task
        updateTask: async (id, updates)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { taskService } = get();
                const result = await taskService.updateTask({
                    id,
                    ...updates
                });
                if (result.success) {
                    set((state)=>({
                            tasks: state.tasks.map((task)=>task.id === id ? result.data : task),
                            loading: false
                        }));
                } else {
                    set({
                        error: result.error || 'Failed to update task',
                        loading: false
                    });
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to update task';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        // Delete task
        deleteTask: async (id)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { taskService } = get();
                const result = await taskService.deleteTask(id);
                if (result.success) {
                    set((state)=>({
                            tasks: state.tasks.filter((task)=>task.id !== id),
                            loading: false
                        }));
                } else {
                    set({
                        error: result.error || 'Failed to delete task',
                        loading: false
                    });
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to delete task';
                set({
                    error: errorMessage,
                    loading: false
                });
                throw error;
            }
        },
        // Complete task
        completeTask: async (id, actualDuration, satisfaction)=>{
            try {
                const { taskService, planningService } = get();
                const task = get().tasks.find((t)=>t.id === id);
                if (!task) throw new Error('Task not found');
                // Complete task using task service
                const result = await taskService.completeTask({
                    id,
                    actualDuration,
                    satisfactionScore: satisfaction
                });
                if (result.success) {
                    // Update local state
                    set((state)=>({
                            tasks: state.tasks.map((t)=>t.id === id ? result.data : t)
                        }));
                    // Update daily stats
                    await planningService.updateActivityStats(task.userId, task.category, actualDuration || task.estimatedDuration);
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to complete task';
                set({
                    error: errorMessage
                });
                throw error;
            }
        },
        // Postpone task
        postponeTask: async (id, reason)=>{
            try {
                const { taskService } = get();
                const result = await taskService.postponeTask({
                    id,
                    reason
                });
                if (result.success) {
                    // Update local state
                    set((state)=>({
                            tasks: state.tasks.map((t)=>t.id === id ? result.data : t)
                        }));
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to postpone task';
                set({
                    error: errorMessage
                });
                throw error;
            }
        },
        // Generate daily schedule
        generateDailySchedule: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { tasks, planningService } = get();
                const userTasks = tasks.filter((task)=>task.userId === userId);
                // 获取用户时间配置
                let userTimeConfig = null;
                try {
                    const { data: userProfile } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from('user_profiles').select('time_config').eq('id', userId).single();
                    userTimeConfig = userProfile?.time_config;
                } catch (error) {
                    console.log('No user time config found, using defaults');
                }
                // 使用新的规划服务
                const result = await planningService.generateDailyPlan(userId, userTasks, userTimeConfig);
                if (result.success) {
                    set({
                        dailySchedule: result.data.schedule,
                        loading: false
                    });
                } else {
                    set({
                        error: result.error || 'Failed to generate schedule',
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to generate schedule';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Analyze balance
        analyzeBalance: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { planningService } = get();
                const result = await planningService.getBalanceAnalysis(userId);
                if (result.success) {
                    set({
                        balanceAnalysis: result.data,
                        loading: false
                    });
                } else {
                    set({
                        error: result.error || 'Failed to analyze balance',
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to analyze balance';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Analyze postponed tasks
        analyzePostponedTasks: async (userId)=>{
            try {
                set({
                    loading: true,
                    error: null
                });
                const { tasks, planningService } = get();
                const userTasks = tasks.filter((task)=>task.userId === userId);
                const result = await planningService.getPostponedTasksAnalysis(userTasks);
                if (result.success) {
                    set({
                        postponedAlerts: result.data.alerts,
                        loading: false
                    });
                } else {
                    set({
                        error: result.error || 'Failed to analyze postponed tasks',
                        loading: false
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to analyze postponed tasks';
                set({
                    error: errorMessage,
                    loading: false
                });
            }
        },
        // Utility actions
        setLoading: (loading)=>set({
                loading
            }),
        setError: (error)=>set({
                error
            }),
        clearError: ()=>set({
                error: null
            })
    }));
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Dashboard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useAuthStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useTaskStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/useTaskStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/target.js [app-client] (ecmascript) <export default as Target>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function Dashboard() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"])();
    const { tasks, dailySchedule, balanceAnalysis, postponedAlerts, fetchTasks, generateDailySchedule, analyzeBalance, analyzePostponedTasks, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useTaskStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTaskStore"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Dashboard.useEffect": ()=>{
            if (!user) {
                router.push('/auth/signin');
                return;
            }
            // 初始化数据
            const initializeData = {
                "Dashboard.useEffect.initializeData": async ()=>{
                    try {
                        await fetchTasks(user.id);
                        await generateDailySchedule(user.id);
                        await analyzeBalance(user.id);
                        await analyzePostponedTasks(user.id);
                    } catch (error) {
                        console.error('Failed to initialize dashboard data:', error);
                    }
                }
            }["Dashboard.useEffect.initializeData"];
            initializeData();
        }
    }["Dashboard.useEffect"], [
        user,
        router,
        fetchTasks,
        generateDailySchedule,
        analyzeBalance,
        analyzePostponedTasks
    ]);
    const handleSignOut = async ()=>{
        try {
            await signOut();
            router.push('/auth/signin');
        } catch (error) {
            console.error('Sign out failed:', error);
        }
    };
    if (!user) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/page.tsx",
                lineNumber: 57,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/page.tsx",
            lineNumber: 56,
            columnNumber: 7
        }, this);
    }
    const todayTasks = tasks.filter((task)=>{
        const today = new Date();
        const taskDate = new Date(task.deadline);
        return taskDate.toDateString() === today.toDateString() && task.status !== 'completed';
    });
    const completedToday = tasks.filter((task)=>{
        const today = new Date();
        const taskDate = new Date(task.updatedAt);
        return taskDate.toDateString() === today.toDateString() && task.status === 'completed';
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "bg-white shadow-sm border-b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between items-center h-16",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-2xl font-bold text-gray-900",
                                        children: "TimeManager"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 81,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "ml-3 text-sm text-gray-500",
                                        children: "智能时间规划助手"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 82,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 80,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm text-gray-700",
                                        children: [
                                            "欢迎，",
                                            user.email
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 86,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>router.push('/settings'),
                                        className: "p-2 text-gray-400 hover:text-gray-600 rounded-md",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                            className: "h-5 w-5"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 91,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 87,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handleSignOut,
                                        className: "text-sm text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md border border-gray-300 hover:bg-gray-50",
                                        children: "退出登录"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 93,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 85,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/page.tsx",
                        lineNumber: 79,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/page.tsx",
                    lineNumber: 78,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/page.tsx",
                lineNumber: 77,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow p-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"], {
                                                className: "h-8 w-8 text-blue-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 111,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 110,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "ml-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm font-medium text-gray-500",
                                                    children: "今日任务"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 114,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-2xl font-semibold text-gray-900",
                                                    children: todayTasks.length
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 115,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 113,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 109,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 108,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow p-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                                className: "h-8 w-8 text-green-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 123,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 122,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "ml-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm font-medium text-gray-500",
                                                    children: "已完成"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 126,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-2xl font-semibold text-gray-900",
                                                    children: completedToday.length
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 127,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 125,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 121,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 120,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow p-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                                className: "h-8 w-8 text-yellow-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 135,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 134,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "ml-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm font-medium text-gray-500",
                                                    children: "生活平衡"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 138,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-2xl font-semibold text-gray-900",
                                                    children: [
                                                        balanceAnalysis?.balanceScore || 0,
                                                        "分"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 139,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 137,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 133,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 132,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg shadow p-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                className: "h-8 w-8 text-purple-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 149,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 148,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "ml-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm font-medium text-gray-500",
                                                    children: "推迟提醒"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 152,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-2xl font-semibold text-gray-900",
                                                    children: postponedAlerts.length
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 153,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 151,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 147,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 146,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/page.tsx",
                        lineNumber: 107,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-white rounded-lg shadow p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-lg font-semibold text-gray-900 mb-4",
                                    children: "快速操作"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 162,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 md:grid-cols-3 gap-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>router.push('/tasks/new'),
                                            className: "flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-indigo-500 hover:bg-indigo-50 transition-colors",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                    className: "h-6 w-6 text-gray-400 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 168,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-gray-600",
                                                    children: "添加新任务"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 169,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 164,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>router.push('/planning'),
                                            className: "flex items-center justify-center p-4 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                    className: "h-6 w-6 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 176,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "查看今日规划"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 172,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>router.push('/tasks'),
                                            className: "flex items-center justify-center p-4 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"], {
                                                    className: "h-6 w-6 mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 184,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "管理所有任务"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 185,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 180,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 163,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/page.tsx",
                            lineNumber: 161,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/page.tsx",
                        lineNumber: 160,
                        columnNumber: 9
                    }, this),
                    dailySchedule && dailySchedule.timeSlots.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-white rounded-lg shadow p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-lg font-semibold text-gray-900 mb-4",
                                    children: "今日规划预览"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 195,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-3",
                                    children: [
                                        dailySchedule.timeSlots.slice(0, 3).map((slot, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center p-3 bg-gray-50 rounded-lg",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex-shrink-0 w-20 text-sm text-gray-500",
                                                        children: slot.startTime.toLocaleTimeString('zh-CN', {
                                                            hour: '2-digit',
                                                            minute: '2-digit'
                                                        })
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 199,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "ml-4 flex-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "font-medium text-gray-900",
                                                                children: slot.task.title
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 206,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm text-gray-500",
                                                                children: slot.task.category
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 207,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 205,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex-shrink-0",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",
                                                            children: [
                                                                slot.task.estimatedDuration,
                                                                "分钟"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                                            lineNumber: 210,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 209,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 198,
                                                columnNumber: 19
                                            }, this)),
                                        dailySchedule.timeSlots.length > 3 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>router.push('/planning'),
                                                className: "text-indigo-600 hover:text-indigo-500 text-sm font-medium",
                                                children: [
                                                    "查看完整规划 (",
                                                    dailySchedule.timeSlots.length - 3,
                                                    " 个更多任务)"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 218,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 217,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 196,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/page.tsx",
                            lineNumber: 194,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/page.tsx",
                        lineNumber: 193,
                        columnNumber: 11
                    }, this),
                    balanceAnalysis && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-white rounded-lg shadow p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-lg font-semibold text-gray-900 mb-4",
                                    children: "生活平衡分析"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 235,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-500",
                                                    children: "工作"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 238,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-2xl font-semibold text-blue-600",
                                                    children: [
                                                        Math.round(balanceAnalysis.workRatio * 100),
                                                        "%"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 239,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 237,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-500",
                                                    children: "提升"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 244,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-2xl font-semibold text-green-600",
                                                    children: [
                                                        Math.round(balanceAnalysis.improvementRatio * 100),
                                                        "%"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 245,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 243,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-500",
                                                    children: "娱乐"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 250,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-2xl font-semibold text-purple-600",
                                                    children: [
                                                        Math.round(balanceAnalysis.entertainmentRatio * 100),
                                                        "%"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                                    lineNumber: 251,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 249,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 236,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-50 rounded-lg p-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-gray-700",
                                        children: balanceAnalysis.recommendation
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                        lineNumber: 257,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 256,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/page.tsx",
                            lineNumber: 234,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/page.tsx",
                        lineNumber: 233,
                        columnNumber: 11
                    }, this),
                    postponedAlerts.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-yellow-50 border border-yellow-200 rounded-lg p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-lg font-semibold text-yellow-800 mb-4",
                                    children: "⚠️ 推迟任务提醒"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 267,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-3",
                                    children: [
                                        postponedAlerts.slice(0, 2).map((alert, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white rounded-lg p-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between items-start",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "font-medium text-gray-900",
                                                                        children: alert.task.title
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                                        lineNumber: 273,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-sm text-gray-500",
                                                                        children: [
                                                                            "已推迟 ",
                                                                            alert.postponeCount,
                                                                            " 次 • ",
                                                                            alert.daysSinceCreated,
                                                                            " 天前创建"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                                        lineNumber: 274,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 272,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${alert.urgencyLevel === 'critical' ? 'bg-red-100 text-red-800' : alert.urgencyLevel === 'high' ? 'bg-orange-100 text-orange-800' : 'bg-yellow-100 text-yellow-800'}`,
                                                                children: alert.urgencyLevel
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                                lineNumber: 278,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 271,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-600 mt-2",
                                                        children: alert.suggestion
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/page.tsx",
                                                        lineNumber: 286,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 270,
                                                columnNumber: 19
                                            }, this)),
                                        postponedAlerts.length > 2 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>router.push('/tasks?filter=postponed'),
                                                className: "text-yellow-700 hover:text-yellow-600 text-sm font-medium",
                                                children: [
                                                    "查看所有推迟任务 (",
                                                    postponedAlerts.length - 2,
                                                    " 个更多)"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/page.tsx",
                                                lineNumber: 291,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/page.tsx",
                                            lineNumber: 290,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/page.tsx",
                                    lineNumber: 268,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/page.tsx",
                            lineNumber: 266,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/page.tsx",
                        lineNumber: 265,
                        columnNumber: 11
                    }, this),
                    loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 306,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-500 mt-2",
                                children: "正在加载数据..."
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/page.tsx",
                                lineNumber: 307,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/page.tsx",
                        lineNumber: 305,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/page.tsx",
                lineNumber: 105,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/page.tsx",
        lineNumber: 75,
        columnNumber: 5
    }, this);
}
_s(Dashboard, "SRIx+J6waMDKUKRa/Dk5llAUgLE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useAuthStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$useTaskStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTaskStore"]
    ];
});
_c = Dashboard;
var _c;
__turbopack_context__.k.register(_c, "Dashboard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_750e4c55._.js.map